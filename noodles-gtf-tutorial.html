<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Noodles GTF Tutorial - Complete Guide to GTF Handling in Rust</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --background-color: #ecf0f1;
            --text-color: #2c3e50;
            --code-bg: #f8f9fa;
            --border-color: #bdc3c7;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem 0;
            text-align: center;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .toc {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            border-left: 4px solid var(--secondary-color);
        }

        .toc h2 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .toc ul {
            list-style: none;
        }

        .toc li {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }

        .toc a {
            color: var(--secondary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .toc a:hover {
            color: var(--accent-color);
        }

        .section {
            background: white;
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid var(--secondary-color);
        }

        h2 {
            color: var(--primary-color);
            font-size: 2rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
        }

        h3 {
            color: var(--secondary-color);
            font-size: 1.5rem;
            margin: 1.5rem 0 1rem 0;
        }

        h4 {
            color: var(--primary-color);
            font-size: 1.2rem;
            margin: 1rem 0 0.5rem 0;
        }

        .code-block {
            background: var(--code-bg);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .highlight {
            background: linear-gradient(120deg, #a8e6cf 0%, #dcedc1 100%);
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid var(--success-color);
            margin: 1rem 0;
        }

        .warning {
            background: linear-gradient(120deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid var(--warning-color);
            margin: 1rem 0;
        }

        .info {
            background: linear-gradient(120deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }

        .api-method {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .method-signature {
            font-family: 'Courier New', monospace;
            background: var(--primary-color);
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: var(--secondary-color);
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🧬 Noodles GTF Tutorial</h1>
            <p class="subtitle">Complete Guide to GTF Handling in Rust with the Noodles Library</p>
        </header>

        <div class="loading" id="loading">
            <h3>Loading Tutorial Content...</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progress"></div>
            </div>
        </div>

        <div id="content" style="display: none;">
            <!-- Table of Contents -->
            <div class="toc">
                <h2>📚 Table of Contents</h2>
                <ul>
                    <li><a href="#introduction">1. Introduction to GTF and Noodles</a></li>
                    <li><a href="#architecture">2. Library Architecture</a></li>
                    <li><a href="#core-types">3. Core Types and Structures</a></li>
                    <li><a href="#reading">4. Reading GTF Files</a></li>
                    <li><a href="#writing">5. Writing GTF Files</a></li>
                    <li><a href="#querying">6. Querying and Indexing</a></li>
                    <li><a href="#attributes">7. Working with Attributes</a></li>
                    <li><a href="#examples">8. Practical Examples</a></li>
                    <li><a href="#best-practices">9. Best Practices</a></li>
                    <li><a href="#visualization">10. Data Flow Visualization</a></li>
                </ul>
            </div>

            <!-- Introduction Section -->
            <section id="introduction" class="section">
                <h2>🧬 Introduction to GTF and Noodles</h2>

                <h3>What is GTF?</h3>
                <p>The <strong>Gene Transfer Format (GTF)</strong> is a tab-delimited text format used to represent genomic features and annotations. It's widely used in bioinformatics for storing information about genes, transcripts, exons, and other genomic elements.</p>

                <div class="highlight">
                    <h4>GTF Format Structure</h4>
                    <p>Each GTF record contains 9 tab-separated fields:</p>
                    <ol>
                        <li><strong>seqname</strong> - Reference sequence name</li>
                        <li><strong>source</strong> - Source of the annotation</li>
                        <li><strong>feature</strong> - Feature type (gene, transcript, exon, etc.)</li>
                        <li><strong>start</strong> - Start position (1-based)</li>
                        <li><strong>end</strong> - End position (1-based, inclusive)</li>
                        <li><strong>score</strong> - Confidence score (or '.')</li>
                        <li><strong>strand</strong> - Strand ('+', '-', or '.')</li>
                        <li><strong>frame</strong> - Reading frame (0, 1, 2, or '.')</li>
                        <li><strong>attributes</strong> - Semicolon-separated key-value pairs</li>
                    </ol>
                </div>

                <h3>Why Noodles GTF?</h3>
                <p>The <code>noodles-gtf</code> crate provides a robust, efficient, and type-safe way to handle GTF files in Rust. It offers:</p>
                <ul>
                    <li>🚀 <strong>High Performance</strong> - Zero-copy parsing where possible</li>
                    <li>🔒 <strong>Memory Safety</strong> - Rust's ownership system prevents common errors</li>
                    <li>🎯 <strong>Type Safety</strong> - Strong typing for genomic coordinates and features</li>
                    <li>📚 <strong>Rich API</strong> - Comprehensive methods for reading, writing, and querying</li>
                    <li>🔍 <strong>Indexing Support</strong> - Fast random access with CSI indices</li>
                </ul>

                <div class="code-block">
// Example GTF record
chr1    HAVANA  gene    11869   14409   .   +   .   gene_id "ENSG00000223972"; gene_name "DDX11L1";
chr1    HAVANA  transcript  11869   14409   .   +   .   gene_id "ENSG00000223972"; transcript_id "ENST00000456328";
                </div>
            </section>

            <!-- Architecture Section -->
            <section id="architecture" class="section">
                <h2>🏗️ Library Architecture</h2>

                <h3>Module Structure</h3>
                <p>The <code>noodles-gtf</code> library is organized into several key modules:</p>

                <div class="api-method">
                    <h4>Core Modules</h4>
                    <ul>
                        <li><code>io</code> - Input/output operations (Reader, Writer)</li>
                        <li><code>record</code> - GTF record representation and parsing</li>
                        <li><code>line</code> - Line-level parsing and classification</li>
                        <li><code>line_buf</code> - Buffered line operations</li>
                    </ul>
                </div>

                <h3>Dependency Graph</h3>
                <p>The library builds upon several foundational crates:</p>
                <ul>
                    <li><code>noodles-gff</code> - Shared GFF/GTF functionality</li>
                    <li><code>noodles-core</code> - Core genomic types (Position, Region)</li>
                    <li><code>noodles-bgzf</code> - Block GZIP compression support</li>
                    <li><code>noodles-csi</code> - Coordinate-sorted indexing</li>
                    <li><code>bstr</code> - Byte string handling</li>
                    <li><code>indexmap</code> - Ordered hash maps for attributes</li>
                </ul>

                <div class="info">
                    <h4>💡 Design Philosophy</h4>
                    <p>The library follows Rust's zero-cost abstractions principle, providing high-level APIs without sacrificing performance. It leverages the type system to prevent common genomics programming errors at compile time.</p>
                </div>

                <!-- SVG Architecture Diagram -->
                <div style="text-align: center; margin: 2rem 0;">
                    <svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Noodles GTF Architecture</text>

                        <!-- Application Layer -->
                        <rect x="50" y="60" width="700" height="60" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="5"/>
                        <text x="400" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Application Layer</text>
                        <text x="400" y="105" text-anchor="middle" font-size="12" fill="white">Your Rust Application</text>

                        <!-- GTF API Layer -->
                        <rect x="50" y="140" width="700" height="80" fill="#27ae60" stroke="#229954" stroke-width="2" rx="5"/>
                        <text x="400" y="165" text-anchor="middle" font-size="14" font-weight="bold" fill="white">noodles-gtf API</text>

                        <!-- API Components -->
                        <rect x="70" y="175" width="120" height="35" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="130" y="195" text-anchor="middle" font-size="10" fill="white">Reader/Writer</text>

                        <rect x="210" y="175" width="120" height="35" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="270" y="195" text-anchor="middle" font-size="10" fill="white">Record</text>

                        <rect x="350" y="175" width="120" height="35" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="410" y="195" text-anchor="middle" font-size="10" fill="white">Attributes</text>

                        <rect x="490" y="175" width="120" height="35" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="550" y="195" text-anchor="middle" font-size="10" fill="white">Line</text>

                        <rect x="630" y="175" width="100" height="35" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="680" y="195" text-anchor="middle" font-size="10" fill="white">Query</text>

                        <!-- Foundation Layer -->
                        <rect x="50" y="240" width="700" height="80" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="5"/>
                        <text x="400" y="265" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Foundation Libraries</text>

                        <!-- Foundation Components -->
                        <rect x="70" y="275" width="100" height="35" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                        <text x="120" y="295" text-anchor="middle" font-size="9" fill="white">noodles-gff</text>

                        <rect x="190" y="275" width="100" height="35" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                        <text x="240" y="295" text-anchor="middle" font-size="9" fill="white">noodles-core</text>

                        <rect x="310" y="275" width="100" height="35" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                        <text x="360" y="295" text-anchor="middle" font-size="9" fill="white">noodles-bgzf</text>

                        <rect x="430" y="275" width="100" height="35" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                        <text x="480" y="295" text-anchor="middle" font-size="9" fill="white">noodles-csi</text>

                        <rect x="550" y="275" width="80" height="35" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                        <text x="590" y="295" text-anchor="middle" font-size="9" fill="white">bstr</text>

                        <rect x="650" y="275" width="80" height="35" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                        <text x="690" y="295" text-anchor="middle" font-size="9" fill="white">indexmap</text>

                        <!-- Data Layer -->
                        <rect x="50" y="340" width="700" height="40" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="5"/>
                        <text x="400" y="365" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Data Layer (GTF Files, Indices)</text>

                        <!-- Arrows -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                            </marker>
                        </defs>

                        <!-- Vertical arrows -->
                        <line x1="400" y1="120" x2="400" y2="135" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="400" y1="220" x2="400" y2="235" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="400" y1="320" x2="400" y2="335" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    </svg>
                </div>
            </section>

            <!-- Core Types Section -->
            <section id="core-types" class="section">
                <h2>🔧 Core Types and Structures</h2>

                <h3>Record Structure</h3>
                <p>The <code>Record</code> type is the fundamental building block for representing GTF entries:</p>

                <div class="api-method">
                    <div class="method-signature">pub struct Record&lt;'l&gt;(Fields&lt;'l&gt;)</div>
                    <p>A zero-copy GTF record that borrows from the source data. Key methods include:</p>
                    <ul>
                        <li><code>reference_sequence_name() -&gt; &amp;BStr</code> - Get chromosome/contig name</li>
                        <li><code>source() -&gt; &amp;BStr</code> - Get annotation source</li>
                        <li><code>ty() -&gt; &amp;BStr</code> - Get feature type (gene, transcript, exon)</li>
                        <li><code>start() -&gt; io::Result&lt;Position&gt;</code> - Get start position (1-based)</li>
                        <li><code>end() -&gt; io::Result&lt;Position&gt;</code> - Get end position (1-based, inclusive)</li>
                        <li><code>score() -&gt; Option&lt;io::Result&lt;f32&gt;&gt;</code> - Get confidence score</li>
                        <li><code>strand() -&gt; io::Result&lt;Strand&gt;</code> - Get strand information</li>
                        <li><code>phase() -&gt; Option&lt;io::Result&lt;Phase&gt;&gt;</code> - Get reading frame</li>
                        <li><code>attributes() -&gt; io::Result&lt;Attributes&gt;</code> - Get key-value attributes</li>
                    </ul>
                </div>

                <h3>Line Types</h3>
                <p>GTF files contain different types of lines, represented by the <code>Line</code> enum:</p>

                <div class="code-block">
use noodles_gtf::{Line, line::Kind};

// Line classification
match line.kind() {
    Kind::Comment => {
        // Handle comment lines (starting with #)
        if let Some(comment) = line.as_comment() {
            println!("Comment: {}", comment);
        }
    }
    Kind::Record => {
        // Handle GTF records
        if let Some(result) = line.as_record() {
            let record = result?;
            println!("Feature: {}", record.ty());
        }
    }
}
                </div>

                <h3>Attributes System</h3>
                <p>GTF attributes are key-value pairs that provide additional information about features:</p>

                <div class="highlight">
                    <h4>Attribute Parsing</h4>
                    <p>The library automatically parses attribute strings like:</p>
                    <code>gene_id "ENSG00000223972"; gene_name "DDX11L1"; transcript_id "ENST00000456328";</code>
                </div>

                <div class="api-method">
                    <div class="method-signature">pub struct Attributes&lt;'r&gt;(IndexMap&lt;&amp;'r BStr, Value&lt;'r&gt;&gt;)</div>
                    <p>Provides efficient access to attribute data:</p>
                    <ul>
                        <li><code>is_empty() -&gt; bool</code> - Check if attributes exist</li>
                        <li><code>get(key: &amp;[u8]) -&gt; Option&lt;io::Result&lt;&amp;Value&gt;&gt;</code> - Get attribute value</li>
                        <li><code>iter() -&gt; Iterator</code> - Iterate over all key-value pairs</li>
                    </ul>
                </div>

                <h3>Genomic Coordinates</h3>
                <p>The library uses strong typing for genomic positions:</p>

                <div class="code-block">
use noodles_core::Position;

// Positions are 1-based and guaranteed to be valid
let start: Position = record.start()?;
let end: Position = record.end()?;

// Mathematical operations on positions
let length = usize::from(end) - usize::from(start) + 1;
                </div>

                <div class="warning">
                    <h4>⚠️ Coordinate System</h4>
                    <p>GTF uses 1-based, fully-closed intervals [start, end]. This differs from some other formats like BED which use 0-based, half-open intervals.</p>
                </div>
            </section>

            <!-- Reading Section -->
            <section id="reading" class="section">
                <h2>📖 Reading GTF Files</h2>

                <h3>Basic File Reading</h3>
                <p>The <code>Reader</code> provides multiple ways to read GTF data:</p>

                <div class="code-block">
use std::{fs::File, io::BufReader};
use noodles_gtf as gtf;

// Open a GTF file
let mut reader = File::open("annotations.gtf")
    .map(BufReader::new)
    .map(gtf::io::Reader::new)?;

// Method 1: Line-by-line reading
let mut line = gtf::Line::default();
while reader.read_line(&mut line)? != 0 {
    match line.kind() {
        gtf::line::Kind::Comment => {
            println!("Comment: {}", line.as_comment().unwrap());
        }
        gtf::line::Kind::Record => {
            let record = line.as_record().unwrap()?;
            println!("Feature: {} at {}:{}-{}",
                record.ty(),
                record.reference_sequence_name(),
                record.start()?,
                record.end()?
            );
        }
    }
}
                </div>

                <h3>Iterator-based Reading</h3>
                <p>For more convenient processing, use the iterator interface:</p>

                <div class="code-block">
// Method 2: Using record iterator
for result in reader.record_bufs() {
    let record = result?;

    // Access record fields
    println!("Gene: {} on chromosome {}",
        record.ty(),
        record.reference_sequence_name()
    );

    // Work with attributes
    let attributes = record.attributes();
    if let Some(gene_id) = attributes.get(b"gene_id") {
        println!("Gene ID: {:?}", gene_id);
    }
}
                </div>

                <h3>Compressed File Support</h3>
                <p>The library seamlessly handles bgzip-compressed files:</p>

                <div class="code-block">
use noodles_bgzf as bgzf;

// Reading compressed GTF files
let mut reader = File::open("annotations.gtf.gz")
    .map(bgzf::io::Reader::new)
    .map(gtf::io::Reader::new)?;

// Same API as uncompressed files
for result in reader.record_bufs() {
    let record = result?;
    // Process record...
}
                </div>

                <div class="info">
                    <h4>💡 Performance Tip</h4>
                    <p>Use <code>BufReader</code> for better I/O performance when reading large files. The library is designed to minimize memory allocations through zero-copy parsing where possible.</p>
                </div>

                <h3>Error Handling</h3>
                <p>Robust error handling is built into the API:</p>

                <div class="code-block">
use std::io;

match reader.read_line(&mut line) {
    Ok(0) => println!("End of file reached"),
    Ok(n) => println!("Read {} bytes", n),
    Err(e) => match e.kind() {
        io::ErrorKind::InvalidData => {
            eprintln!("Invalid GTF format: {}", e);
        }
        _ => eprintln!("I/O error: {}", e),
    }
}
                </div>
            </section>

            <!-- Writing Section -->
            <section id="writing" class="section">
                <h2>✍️ Writing GTF Files</h2>

                <h3>Basic Writing</h3>
                <p>The <code>Writer</code> provides methods to create GTF files:</p>

                <div class="code-block">
use std::io;
use noodles_gff::feature::RecordBuf;
use noodles_gtf as gtf;

// Create a writer
let stdout = io::stdout().lock();
let mut writer = gtf::io::Writer::new(stdout);

// Create a record
let record = RecordBuf::default();
writer.write_record(&record)?;

// Write to file
let mut file_writer = File::create("output.gtf")
    .map(io::BufWriter::new)
    .map(gtf::io::Writer::new)?;

file_writer.write_record(&record)?;
                </div>

                <h3>Building Records</h3>
                <p>Create custom GTF records using the builder pattern:</p>

                <div class="code-block">
use noodles_gff::feature::RecordBuf;
use noodles_core::Position;

// Build a gene record
let gene_record = RecordBuf::builder()
    .set_reference_sequence_name("chr1")
    .set_source("HAVANA")
    .set_ty("gene")
    .set_start(Position::try_from(11869)?)
    .set_end(Position::try_from(14409)?)
    .set_strand(noodles_gff::feature::record::Strand::Forward)
    .build();

writer.write_record(&gene_record)?;
                </div>

                <h3>Writing with Attributes</h3>
                <p>Add attributes to provide additional annotation information:</p>

                <div class="code-block">
use noodles_gff::feature::record_buf::Attributes;

// Create attributes
let mut attributes = Attributes::default();
attributes.insert("gene_id".into(), "ENSG00000223972".into());
attributes.insert("gene_name".into(), "DDX11L1".into());

let record = RecordBuf::builder()
    .set_reference_sequence_name("chr1")
    .set_source("HAVANA")
    .set_ty("gene")
    .set_start(Position::try_from(11869)?)
    .set_end(Position::try_from(14409)?)
    .set_attributes(attributes)
    .build();

writer.write_record(&record)?;
                </div>
            </section>

            <!-- Querying Section -->
            <section id="querying" class="section">
                <h2>🔍 Querying and Indexing</h2>

                <h3>Indexed Queries</h3>
                <p>For large GTF files, use coordinate-sorted indices (CSI) for fast random access:</p>

                <div class="code-block">
use noodles_bgzf as bgzf;
use noodles_csi as csi;
use noodles_core::Region;

// Load the index
let index = csi::fs::read("annotations.gtf.gz.csi")?;

// Open the compressed GTF file
let mut reader = File::open("annotations.gtf.gz")
    .map(bgzf::io::Reader::new)
    .map(gtf::io::Reader::new)?;

// Define a genomic region
let region: Region = "chr1:10000-20000".parse()?;

// Query the region
let query = reader.query(&index, &region)?;

for result in query {
    let record = result?;
    println!("Found feature: {} at {}:{}-{}",
        record.ty(),
        record.reference_sequence_name(),
        record.start()?,
        record.end()?
    );
}
                </div>

                <h3>Region-based Filtering</h3>
                <p>The query system automatically filters records that intersect with the specified region:</p>

                <div class="highlight">
                    <h4>Query Mathematics</h4>
                    <p>A record intersects a region if:</p>
                    <p>$$\text{max}(\text{record.start}, \text{region.start}) \leq \text{min}(\text{record.end}, \text{region.end})$$</p>
                </div>

                <div class="warning">
                    <h4>⚠️ Index Requirements</h4>
                    <p>Indexed queries require:</p>
                    <ul>
                        <li>GTF file compressed with bgzip</li>
                        <li>Coordinate-sorted records</li>
                        <li>CSI index file (.csi extension)</li>
                    </ul>
                </div>
            </section>

            <!-- Attributes Section -->
            <section id="attributes" class="section">
                <h2>🏷️ Working with Attributes</h2>

                <h3>Attribute Access Patterns</h3>
                <p>GTF attributes contain essential metadata about genomic features:</p>

                <div class="code-block">
// Common GTF attributes
let attributes = record.attributes()?;

// Gene information
if let Some(gene_id) = attributes.get(b"gene_id") {
    println!("Gene ID: {:?}", gene_id?);
}

if let Some(gene_name) = attributes.get(b"gene_name") {
    println!("Gene Name: {:?}", gene_name?);
}

// Transcript information
if let Some(transcript_id) = attributes.get(b"transcript_id") {
    println!("Transcript ID: {:?}", transcript_id?);
}

// Iterate over all attributes
for result in attributes.iter() {
    let (key, value) = result?;
    println!("{}: {:?}", key, value);
}
                </div>

                <h3>Attribute Value Types</h3>
                <p>Attributes can contain different types of values:</p>

                <div class="api-method">
                    <div class="method-signature">pub enum Value&lt;'a&gt;</div>
                    <ul>
                        <li><code>String(Cow&lt;'a, BStr&gt;)</code> - Text values (most common)</li>
                        <li>Values can be quoted or unquoted</li>
                        <li>Escape sequences are automatically handled</li>
                    </ul>
                </div>

                <h3>Multi-value Attributes</h3>
                <p>Some attributes can have multiple values:</p>

                <div class="code-block">
// Handle attributes with multiple values
match attributes.get(b"tag") {
    Some(Ok(Value::String(values))) => {
        // Single value case
        println!("Tag: {}", values);
    }
    Some(Ok(value)) => {
        // Handle other value types
        println!("Tag value: {:?}", value);
    }
    Some(Err(e)) => eprintln!("Error parsing tag: {}", e),
    None => println!("No tag attribute found"),
}
                </div>

                <h3>Escape Sequence Handling</h3>
                <p>The library automatically handles GTF escape sequences:</p>

                <div class="info">
                    <h4>Supported Escape Sequences</h4>
                    <ul>
                        <li><code>\\</code> - Literal backslash</li>
                        <li><code>\"</code> - Literal quotation mark</li>
                    </ul>
                </div>
            </section>

            <!-- Examples Section -->
            <section id="examples" class="section">
                <h2>💡 Practical Examples</h2>

                <h3>Example 1: Count Features by Type</h3>
                <div class="code-block">
use std::collections::HashMap;
use noodles_gtf as gtf;

fn count_features(filename: &str) -> Result&lt;HashMap&lt;String, usize&gt;, Box&lt;dyn std::error::Error&gt;&gt; {
    let mut reader = File::open(filename)
        .map(BufReader::new)
        .map(gtf::io::Reader::new)?;

    let mut counts = HashMap::new();

    for result in reader.record_bufs() {
        let record = result?;
        let feature_type = record.ty().to_string();
        *counts.entry(feature_type).or_insert(0) += 1;
    }

    Ok(counts)
}

// Usage
let counts = count_features("annotations.gtf")?;
for (feature_type, count) in counts {
    println!("{}: {}", feature_type, count);
}
                </div>

                <h3>Example 2: Extract Gene Information</h3>
                <div class="code-block">
#[derive(Debug)]
struct GeneInfo {
    id: String,
    name: Option&lt;String&gt;,
    chromosome: String,
    start: u64,
    end: u64,
    strand: String,
}

fn extract_genes(filename: &str) -> Result&lt;Vec&lt;GeneInfo&gt;, Box&lt;dyn std::error::Error&gt;&gt; {
    let mut reader = File::open(filename)
        .map(BufReader::new)
        .map(gtf::io::Reader::new)?;

    let mut genes = Vec::new();

    for result in reader.record_bufs() {
        let record = result?;

        // Only process gene features
        if record.ty() == "gene" {
            let attributes = record.attributes();

            if let Some(gene_id) = attributes.get(b"gene_id") {
                let gene_name = attributes.get(b"gene_name")
                    .and_then(|v| v.ok())
                    .map(|v| v.to_string());

                let gene_info = GeneInfo {
                    id: gene_id?.to_string(),
                    name: gene_name,
                    chromosome: record.reference_sequence_name().to_string(),
                    start: usize::from(record.start()?) as u64,
                    end: usize::from(record.end()?) as u64,
                    strand: format!("{:?}", record.strand()?),
                };

                genes.push(gene_info);
            }
        }
    }

    Ok(genes)
}
                </div>

                <h3>Example 3: Filter by Genomic Region</h3>
                <div class="code-block">
use noodles_core::{Position, Region};

fn filter_by_region(
    filename: &str,
    target_chr: &str,
    start: u64,
    end: u64
) -> Result&lt;Vec&lt;String&gt;, Box&lt;dyn std::error::Error&gt;&gt; {
    let mut reader = File::open(filename)
        .map(BufReader::new)
        .map(gtf::io::Reader::new)?;

    let mut features = Vec::new();
    let target_start = Position::try_from(start)?;
    let target_end = Position::try_from(end)?;

    for result in reader.record_bufs() {
        let record = result?;

        // Check if record overlaps with target region
        if record.reference_sequence_name() == target_chr {
            let record_start = record.start()?;
            let record_end = record.end()?;

            // Check for overlap
            if record_start &lt;= target_end && record_end &gt;= target_start {
                features.push(format!(
                    "{} {}:{}-{}",
                    record.ty(),
                    record.reference_sequence_name(),
                    usize::from(record_start),
                    usize::from(record_end)
                ));
            }
        }
    }

    Ok(features)
}
                </div>
            </section>

            <!-- Best Practices Section -->
            <section id="best-practices" class="section">
                <h2>🎯 Best Practices</h2>

                <h3>Performance Optimization</h3>
                <div class="highlight">
                    <h4>Memory Efficiency</h4>
                    <ul>
                        <li>Use <code>BufReader</code> for large files to reduce I/O overhead</li>
                        <li>Prefer zero-copy <code>Record</code> over <code>RecordBuf</code> when possible</li>
                        <li>Process records in streaming fashion rather than loading all into memory</li>
                        <li>Use indexed queries for random access patterns</li>
                    </ul>
                </div>

                <h3>Error Handling</h3>
                <div class="code-block">
// Robust error handling pattern
match reader.read_line(&mut line) {
    Ok(0) => break, // EOF
    Ok(_) => {
        match line.as_record() {
            Some(Ok(record)) => {
                // Process valid record
                process_record(&record)?;
            }
            Some(Err(e)) => {
                eprintln!("Invalid record: {}", e);
                // Continue processing or abort based on requirements
            }
            None => {
                // Comment line, skip
            }
        }
    }
    Err(e) => {
        eprintln!("I/O error: {}", e);
        return Err(e.into());
    }
}
                </div>

                <h3>Type Safety</h3>
                <div class="warning">
                    <h4>⚠️ Common Pitfalls</h4>
                    <ul>
                        <li>Always validate positions before arithmetic operations</li>
                        <li>Handle missing attributes gracefully</li>
                        <li>Be aware of 1-based coordinate system</li>
                        <li>Check for empty attribute values</li>
                    </ul>
                </div>

                <h3>Testing Strategies</h3>
                <div class="code-block">
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_gtf_parsing() {
        let gtf_data = b"chr1\tHAVANA\tgene\t11869\t14409\t.\t+\t.\tgene_id \"ENSG00000223972\";\n";
        let mut reader = gtf::io::Reader::new(&gtf_data[..]);

        let mut line = gtf::Line::default();
        assert!(reader.read_line(&mut line).unwrap() > 0);

        let record = line.as_record().unwrap().unwrap();
        assert_eq!(record.reference_sequence_name(), "chr1");
        assert_eq!(record.ty(), "gene");

        let attributes = record.attributes().unwrap();
        assert!(attributes.get(b"gene_id").is_some());
    }
}
                </div>
            </section>

            <!-- Visualization Section -->
            <section id="visualization" class="section">
                <h2>📊 Data Flow Visualization</h2>

                <h3>GTF Processing Pipeline</h3>
                <p>Understanding the data flow through the noodles-gtf library:</p>

                <div style="text-align: center; margin: 2rem 0;">
                    <svg width="900" height="600" viewBox="0 0 900 600" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="900" height="600" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                        <!-- Title -->
                        <text x="450" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">GTF Data Processing Flow</text>

                        <!-- Input Layer -->
                        <rect x="50" y="70" width="800" height="80" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="8"/>
                        <text x="450" y="95" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Input Sources</text>

                        <!-- Input types -->
                        <rect x="80" y="110" width="120" height="30" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="4"/>
                        <text x="140" y="128" text-anchor="middle" font-size="11" fill="white">GTF Files</text>

                        <rect x="220" y="110" width="120" height="30" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="4"/>
                        <text x="280" y="128" text-anchor="middle" font-size="11" fill="white">Compressed GTF</text>

                        <rect x="360" y="110" width="120" height="30" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="4"/>
                        <text x="420" y="128" text-anchor="middle" font-size="11" fill="white">Streams</text>

                        <rect x="500" y="110" width="120" height="30" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="4"/>
                        <text x="560" y="128" text-anchor="middle" font-size="11" fill="white">Memory Buffers</text>

                        <rect x="640" y="110" width="120" height="30" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="4"/>
                        <text x="700" y="128" text-anchor="middle" font-size="11" fill="white">Indexed Files</text>

                        <!-- Reader Layer -->
                        <rect x="50" y="180" width="800" height="60" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="8"/>
                        <text x="450" y="205" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Reader Layer</text>
                        <text x="450" y="225" text-anchor="middle" font-size="12" fill="white">gtf::io::Reader - Line parsing, buffering, decompression</text>

                        <!-- Parsing Layer -->
                        <rect x="50" y="270" width="800" height="80" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="8"/>
                        <text x="450" y="295" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Parsing Layer</text>

                        <!-- Parsing components -->
                        <rect x="100" y="310" width="140" height="30" fill="#a569bd" stroke="#9b59b6" stroke-width="1" rx="4"/>
                        <text x="170" y="328" text-anchor="middle" font-size="10" fill="white">Line Classification</text>

                        <rect x="260" y="310" width="140" height="30" fill="#a569bd" stroke="#9b59b6" stroke-width="1" rx="4"/>
                        <text x="330" y="328" text-anchor="middle" font-size="10" fill="white">Field Extraction</text>

                        <rect x="420" y="310" width="140" height="30" fill="#a569bd" stroke="#9b59b6" stroke-width="1" rx="4"/>
                        <text x="490" y="328" text-anchor="middle" font-size="10" fill="white">Attribute Parsing</text>

                        <rect x="580" y="310" width="140" height="30" fill="#a569bd" stroke="#9b59b6" stroke-width="1" rx="4"/>
                        <text x="650" y="328" text-anchor="middle" font-size="10" fill="white">Type Validation</text>

                        <!-- Record Layer -->
                        <rect x="50" y="380" width="800" height="60" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="8"/>
                        <text x="450" y="405" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Record Representation</text>
                        <text x="450" y="425" text-anchor="middle" font-size="12" fill="white">Record&lt;'a&gt; (zero-copy) or RecordBuf (owned)</text>

                        <!-- Processing Layer -->
                        <rect x="50" y="470" width="800" height="80" fill="#27ae60" stroke="#229954" stroke-width="2" rx="8"/>
                        <text x="450" y="495" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Application Processing</text>

                        <!-- Processing types -->
                        <rect x="80" y="515" width="120" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="140" y="530" text-anchor="middle" font-size="9" fill="white">Filtering</text>

                        <rect x="220" y="515" width="120" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="280" y="530" text-anchor="middle" font-size="9" fill="white">Aggregation</text>

                        <rect x="360" y="515" width="120" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="420" y="530" text-anchor="middle" font-size="9" fill="white">Transformation</text>

                        <rect x="500" y="515" width="120" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="560" y="530" text-anchor="middle" font-size="9" fill="white">Analysis</text>

                        <rect x="640" y="515" width="120" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="700" y="530" text-anchor="middle" font-size="9" fill="white">Querying</text>

                        <!-- Arrows -->
                        <defs>
                            <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                            </marker>
                        </defs>

                        <!-- Vertical flow arrows -->
                        <line x1="450" y1="150" x2="450" y2="175" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>
                        <line x1="450" y1="240" x2="450" y2="265" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>
                        <line x1="450" y1="350" x2="450" y2="375" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>
                        <line x1="450" y1="440" x2="450" y2="465" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>

                        <!-- Side annotations -->
                        <text x="20" y="115" font-size="12" fill="#7f8c8d" transform="rotate(-90, 20, 115)">Data Sources</text>
                        <text x="20" y="210" font-size="12" fill="#7f8c8d" transform="rotate(-90, 20, 210)">I/O Layer</text>
                        <text x="20" y="310" font-size="12" fill="#7f8c8d" transform="rotate(-90, 20, 310)">Parsing</text>
                        <text x="20" y="410" font-size="12" fill="#7f8c8d" transform="rotate(-90, 20, 410)">Objects</text>
                        <text x="20" y="510" font-size="12" fill="#7f8c8d" transform="rotate(-90, 20, 510)">Application</text>
                    </svg>
                </div>

                <h3>Memory Layout Visualization</h3>
                <p>Understanding how GTF records are represented in memory:</p>

                <div style="text-align: center; margin: 2rem 0;">
                    <svg width="800" height="300" viewBox="0 0 800 300" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="800" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">GTF Record Memory Layout</text>

                        <!-- Raw GTF Line -->
                        <rect x="50" y="50" width="700" height="40" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="4"/>
                        <text x="60" y="65" font-size="10" fill="#2c3e50">Raw GTF Line:</text>
                        <text x="60" y="80" font-size="9" font-family="monospace" fill="#2c3e50">chr1    HAVANA    gene    11869    14409    .    +    .    gene_id "ENSG00000223972"; gene_name "DDX11L1";</text>

                        <!-- Field breakdown -->
                        <text x="60" y="110" font-size="12" font-weight="bold" fill="#2c3e50">Parsed Fields:</text>

                        <!-- Field boxes -->
                        <rect x="60" y="120" width="80" height="25" fill="#3498db" stroke="#2980b9" stroke-width="1" rx="3"/>
                        <text x="100" y="135" text-anchor="middle" font-size="9" fill="white">seqname</text>
                        <text x="100" y="150" text-anchor="middle" font-size="8" fill="#2c3e50">chr1</text>

                        <rect x="150" y="120" width="80" height="25" fill="#e74c3c" stroke="#c0392b" stroke-width="1" rx="3"/>
                        <text x="190" y="135" text-anchor="middle" font-size="9" fill="white">source</text>
                        <text x="190" y="150" text-anchor="middle" font-size="8" fill="#2c3e50">HAVANA</text>

                        <rect x="240" y="120" width="80" height="25" fill="#27ae60" stroke="#229954" stroke-width="1" rx="3"/>
                        <text x="280" y="135" text-anchor="middle" font-size="9" fill="white">feature</text>
                        <text x="280" y="150" text-anchor="middle" font-size="8" fill="#2c3e50">gene</text>

                        <rect x="330" y="120" width="60" height="25" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
                        <text x="360" y="135" text-anchor="middle" font-size="9" fill="white">start</text>
                        <text x="360" y="150" text-anchor="middle" font-size="8" fill="#2c3e50">11869</text>

                        <rect x="400" y="120" width="60" height="25" fill="#9b59b6" stroke="#8e44ad" stroke-width="1" rx="3"/>
                        <text x="430" y="135" text-anchor="middle" font-size="9" fill="white">end</text>
                        <text x="430" y="150" text-anchor="middle" font-size="8" fill="#2c3e50">14409</text>

                        <rect x="470" y="120" width="50" height="25" fill="#34495e" stroke="#2c3e50" stroke-width="1" rx="3"/>
                        <text x="495" y="135" text-anchor="middle" font-size="9" fill="white">score</text>
                        <text x="495" y="150" text-anchor="middle" font-size="8" fill="#2c3e50">.</text>

                        <rect x="530" y="120" width="50" height="25" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                        <text x="555" y="135" text-anchor="middle" font-size="9" fill="white">strand</text>
                        <text x="555" y="150" text-anchor="middle" font-size="8" fill="#2c3e50">+</text>

                        <rect x="590" y="120" width="50" height="25" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1" rx="3"/>
                        <text x="615" y="135" text-anchor="middle" font-size="9" fill="white">frame</text>
                        <text x="615" y="150" text-anchor="middle" font-size="8" fill="#2c3e50">.</text>

                        <!-- Attributes section -->
                        <rect x="60" y="170" width="680" height="40" fill="#f1c40f" stroke="#f39c12" stroke-width="1" rx="3"/>
                        <text x="400" y="185" text-anchor="middle" font-size="9" fill="white">attributes</text>
                        <text x="70" y="200" font-size="8" font-family="monospace" fill="#2c3e50">gene_id "ENSG00000223972"; gene_name "DDX11L1";</text>

                        <!-- Attribute breakdown -->
                        <text x="60" y="230" font-size="12" font-weight="bold" fill="#2c3e50">Parsed Attributes (IndexMap):</text>

                        <rect x="60" y="240" width="150" height="25" fill="#16a085" stroke="#138d75" stroke-width="1" rx="3"/>
                        <text x="135" y="255" text-anchor="middle" font-size="9" fill="white">gene_id → "ENSG00000223972"</text>

                        <rect x="220" y="240" width="150" height="25" fill="#16a085" stroke="#138d75" stroke-width="1" rx="3"/>
                        <text x="295" y="255" text-anchor="middle" font-size="9" fill="white">gene_name → "DDX11L1"</text>
                    </svg>
                </div>

                <div class="highlight">
                    <h4>🎯 Key Takeaways</h4>
                    <ul>
                        <li><strong>Zero-copy parsing</strong> - Records borrow from source data when possible</li>
                        <li><strong>Structured access</strong> - Type-safe methods for each field</li>
                        <li><strong>Efficient attributes</strong> - IndexMap preserves order and provides fast lookup</li>
                        <li><strong>Memory safety</strong> - Rust's ownership prevents common parsing errors</li>
                    </ul>
                </div>

                <h3>Conclusion</h3>
                <p>The <code>noodles-gtf</code> library provides a comprehensive, efficient, and safe way to work with GTF files in Rust. Its design emphasizes performance through zero-copy parsing, safety through Rust's type system, and usability through a rich API that covers all common GTF processing tasks.</p>

                <div class="info">
                    <h4>🚀 Next Steps</h4>
                    <p>To get started with noodles-gtf:</p>
                    <ol>
                        <li>Add <code>noodles-gtf = "0.46"</code> to your Cargo.toml</li>
                        <li>Explore the examples in the repository</li>
                        <li>Check the API documentation for detailed method signatures</li>
                        <li>Join the community for support and contributions</li>
                    </ol>
                </div>
            </section>
        </div>
    </div>

    <script>
        // Simulate loading progress
        let progress = 0;
        const progressBar = document.getElementById('progress');
        const loadingDiv = document.getElementById('loading');
        const contentDiv = document.getElementById('content');

        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
                setTimeout(() => {
                    loadingDiv.style.display = 'none';
                    contentDiv.style.display = 'block';
                    loadContent();
                }, 500);
            }
            progressBar.style.width = progress + '%';
        }, 200);

        function loadContent() {
            // Add smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add copy functionality to code blocks
            document.querySelectorAll('.code-block').forEach(block => {
                const copyButton = document.createElement('button');
                copyButton.textContent = '📋 Copy';
                copyButton.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    background: #3498db;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                `;

                block.style.position = 'relative';
                block.appendChild(copyButton);

                copyButton.addEventListener('click', () => {
                    const text = block.textContent.replace('📋 Copy', '').trim();
                    navigator.clipboard.writeText(text).then(() => {
                        copyButton.textContent = '✅ Copied!';
                        setTimeout(() => {
                            copyButton.textContent = '📋 Copy';
                        }, 2000);
                    });
                });
            });

            // Add section highlighting on scroll
            const sections = document.querySelectorAll('.section');
            const tocLinks = document.querySelectorAll('.toc a');

            function highlightCurrentSection() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (window.scrollY >= sectionTop - 100) {
                        current = section.getAttribute('id');
                    }
                });

                tocLinks.forEach(link => {
                    link.style.color = '';
                    if (link.getAttribute('href') === '#' + current) {
                        link.style.color = '#e74c3c';
                        link.style.fontWeight = 'bold';
                    }
                });
            }

            window.addEventListener('scroll', highlightCurrentSection);
            highlightCurrentSection(); // Initial call

            // Add interactive elements
            console.log('🧬 Noodles GTF Tutorial loaded successfully!');
            console.log('📚 Navigate using the table of contents');
            console.log('📋 Click copy buttons on code blocks to copy examples');
        }

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K to focus search (if we had search)
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                // Could implement search functionality here
            }

            // Escape to scroll to top
            if (e.key === 'Escape') {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        });

        // Add print styles
        const printStyles = `
            @media print {
                .toc { page-break-after: always; }
                .section { page-break-inside: avoid; }
                .code-block { page-break-inside: avoid; }
                svg { max-width: 100%; height: auto; }
            }
        `;

        const styleSheet = document.createElement('style');
        styleSheet.textContent = printStyles;
        document.head.appendChild(styleSheet);
    </script>
</body>
</html>
