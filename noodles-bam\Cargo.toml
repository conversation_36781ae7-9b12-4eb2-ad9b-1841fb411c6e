[package]
name = "noodles-bam"
version = "0.82.0"
authors = ["<PERSON> <<PERSON><PERSON><PERSON>@gmail.com>"]
license.workspace = true
edition.workspace = true
rust-version.workspace = true
description = "Binary Alignment/Map (BAM) format reader and writer"
homepage = "https://github.com/zaeleus/noodles"
repository = "https://github.com/zaeleus/noodles"
documentation = "https://docs.rs/noodles-bam"
categories = ["parser-implementations", "science::bioinformatics"]

[features]
async = ["dep:futures", "dep:pin-project-lite", "dep:tokio", "noodles-bgzf/async"]

[dependencies]
bstr.workspace = true
byteorder.workspace = true
futures = { workspace = true, optional = true, features = ["std"] }
indexmap.workspace = true
memchr.workspace = true
pin-project-lite = { workspace = true, optional = true }
tokio = { workspace = true, optional = true, features = ["fs", "io-util"] }

noodles-bgzf = { path = "../noodles-bgzf", version = "0.42.0" }
noodles-core = { path = "../noodles-core", version = "0.18.0" }
noodles-csi = { path = "../noodles-csi", version = "0.50.0" }
noodles-sam = { path = "../noodles-sam", version = "0.78.0" }

[dev-dependencies]
flate2.workspace = true
noodles-sam = { path = "../noodles-sam", version = "0.78.0", features = ["async"] }
tokio = { workspace = true, features = ["io-std", "macros", "rt-multi-thread"] }

[lints]
workspace = true

[package.metadata.docs.rs]
features = ["async"]

[[example]]
name = "bam_count_async"
required-features = ["async"]

[[example]]
name = "bam_idxstats_async"
required-features = ["async"]

[[example]]
name = "bam_query_async"
required-features = ["async"]

[[example]]
name = "bam_read_header_async"
required-features = ["async"]

[[example]]
name = "bam_reheader_async"
required-features = ["async"]

[[example]]
name = "bam_view_async"
required-features = ["async"]

[[example]]
name = "bam_write_async"
required-features = ["async"]
