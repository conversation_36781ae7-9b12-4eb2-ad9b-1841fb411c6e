<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Noodles Core Tutorial - Foundation of Genomic Computing in Rust</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --info-color: #8e44ad;
            --core-color: #e67e22;
            --background-color: #ecf0f1;
            --text-color: #2c3e50;
            --code-bg: #f8f9fa;
            --border-color: #bdc3c7;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: linear-gradient(135deg, var(--core-color), var(--secondary-color));
            color: white;
            padding: 2rem 0;
            text-align: center;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .toc {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            border-left: 4px solid var(--core-color);
        }

        .toc h2 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .toc ul {
            list-style: none;
        }

        .toc li {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }

        .toc a {
            color: var(--core-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .toc a:hover {
            color: var(--accent-color);
        }

        .section {
            background: white;
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid var(--core-color);
        }

        h2 {
            color: var(--primary-color);
            font-size: 2rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--core-color);
            padding-bottom: 0.5rem;
        }

        h3 {
            color: var(--core-color);
            font-size: 1.5rem;
            margin: 1.5rem 0 1rem 0;
        }

        h4 {
            color: var(--primary-color);
            font-size: 1.2rem;
            margin: 1rem 0 0.5rem 0;
        }

        .code-block {
            background: var(--code-bg);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
            word-break: break-all;
            overflow-wrap: break-word;
        }

        .highlight {
            background: linear-gradient(120deg, #a8e6cf 0%, #dcedc1 100%);
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid var(--success-color);
            margin: 1rem 0;
        }

        .warning {
            background: linear-gradient(120deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid var(--warning-color);
            margin: 1rem 0;
        }

        .info {
            background: linear-gradient(120deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }

        .core-feature {
            background: linear-gradient(120deg, #fd79a8 0%, #fdcb6e 100%);
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid var(--core-color);
            margin: 1rem 0;
        }

        .api-method {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .method-signature {
            font-family: 'Courier New', monospace;
            background: var(--core-color);
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: var(--core-color);
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--core-color), var(--success-color));
            width: 0%;
            transition: width 0.3s ease;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .feature-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .feature-card h4 {
            color: var(--core-color);
            margin-bottom: 0.5rem;
        }

        .math-formula {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🧬 Noodles Core Tutorial</h1>
            <p class="subtitle">Foundation of Genomic Computing in Rust - Positions, Regions, and Core Types</p>
        </header>

        <div class="loading" id="loading">
            <h3>Loading Core Tutorial Content...</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progress"></div>
            </div>
        </div>

        <div id="content" style="display: none;">
            <!-- Table of Contents -->
            <div class="toc">
                <h2>📚 Table of Contents</h2>
                <ul>
                    <li><a href="#introduction">1. Introduction to Noodles Core</a></li>
                    <li><a href="#architecture">2. Library Architecture</a></li>
                    <li><a href="#position">3. Position Type - 1-based Coordinates</a></li>
                    <li><a href="#sequence-indexing">4. Sequence Indexing</a></li>
                    <li><a href="#intervals">5. Intervals and Ranges</a></li>
                    <li><a href="#regions">6. Genomic Regions</a></li>
                    <li><a href="#coordinate-systems">7. Coordinate Systems</a></li>
                    <li><a href="#mathematical-operations">8. Mathematical Operations</a></li>
                    <li><a href="#parsing">9. Parsing and String Conversion</a></li>
                    <li><a href="#error-handling">10. Error Handling</a></li>
                    <li><a href="#examples">11. Practical Examples</a></li>
                    <li><a href="#best-practices">12. Best Practices</a></li>
                    <li><a href="#visualization">13. Core Concepts Visualization</a></li>
                </ul>
            </div>

            <!-- Introduction Section -->
            <section id="introduction" class="section">
                <h2>🧬 Introduction to Noodles Core</h2>

                <h3>What is Noodles Core?</h3>
                <p>The <strong>noodles-core</strong> crate is the foundational library that provides shared structures and behavior among all noodles libraries. It defines the fundamental types and concepts used throughout the noodles ecosystem for genomic data processing.</p>

                <div class="core-feature">
                    <h4>Core Responsibilities</h4>
                    <ul>
                        <li><strong>Position</strong> - Type-safe 1-based genomic coordinates</li>
                        <li><strong>Region</strong> - Genomic regions with reference sequence names and intervals</li>
                        <li><strong>Interval</strong> - Mathematical intervals for genomic ranges</li>
                        <li><strong>SequenceIndex</strong> - Safe indexing into sequences using 1-based positions</li>
                    </ul>
                </div>

                <h3>Why Type-Safe Genomic Coordinates?</h3>
                <p>Genomic data processing is notorious for coordinate system errors. Different formats use different conventions:</p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>1-based Systems</h4>
                        <ul>
                            <li>GFF3/GTF formats</li>
                            <li>SAM/BAM files</li>
                            <li>VCF files</li>
                            <li>FASTA sequence positions</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>0-based Systems</h4>
                        <ul>
                            <li>BED format</li>
                            <li>Programming arrays</li>
                            <li>Some internal representations</li>
                            <li>Half-open intervals</li>
                        </ul>
                    </div>
                </div>

                <div class="warning">
                    <h4>⚠️ The Off-by-One Problem</h4>
                    <p>Coordinate system confusion is one of the most common sources of bugs in bioinformatics. A single off-by-one error can lead to:</p>
                    <ul>
                        <li>Incorrect feature annotations</li>
                        <li>Wrong variant positions</li>
                        <li>Misaligned sequences</li>
                        <li>Invalid genomic queries</li>
                    </ul>
                </div>

                <h3>Noodles Core Solution</h3>
                <p>Noodles core solves these problems through:</p>
                <ul>
                    <li>🔒 <strong>Type Safety</strong> - Positions cannot be zero, preventing invalid coordinates</li>
                    <li>🎯 <strong>Explicit Conventions</strong> - All positions are clearly 1-based</li>
                    <li>🛡️ <strong>Compile-time Guarantees</strong> - Invalid operations caught at compile time</li>
                    <li>🔄 <strong>Safe Conversions</strong> - Explicit conversion between coordinate systems</li>
                    <li>📐 <strong>Mathematical Correctness</strong> - Proper interval arithmetic</li>
                </ul>

                <div class="code-block">
// Example: Type-safe genomic coordinates
use noodles_core::{Position, Region};

// This won't compile - positions cannot be zero
// let invalid = Position::new(0); // Compilation error!

// Safe position creation
let start = Position::try_from(1000)?;  // 1-based position
let end = Position::try_from(2000)?;

// Create a genomic region
let region = Region::new("chr1", start..=end);
println!("Region: {}", region); // chr1:1000-2000
                </div>
            </section>

            <!-- Architecture Section -->
            <section id="architecture" class="section">
                <h2>🏗️ Library Architecture</h2>

                <h3>Module Structure</h3>
                <p>The noodles-core library is organized into two main modules:</p>

                <div class="api-method">
                    <h4>Core Modules</h4>
                    <ul>
                        <li><code>position</code> - 1-based position type and sequence indexing</li>
                        <li><code>region</code> - Genomic regions and intervals</li>
                    </ul>
                </div>

                <h3>Type Hierarchy</h3>
                <p>The core types form a logical hierarchy:</p>

                <!-- SVG Architecture Diagram -->
                <div style="text-align: center; margin: 2rem 0;">
                    <svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Noodles Core Type Hierarchy</text>

                        <!-- Position Layer -->
                        <rect x="50" y="70" width="700" height="60" fill="#e67e22" stroke="#d35400" stroke-width="2" rx="8"/>
                        <text x="400" y="95" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Position (1-based coordinates)</text>
                        <text x="400" y="115" text-anchor="middle" font-size="12" fill="white">NonZeroUsize wrapper - guarantees valid positions</text>

                        <!-- Interval Layer -->
                        <rect x="50" y="150" width="350" height="80" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="8"/>
                        <text x="225" y="175" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Interval</text>
                        <text x="225" y="195" text-anchor="middle" font-size="11" fill="white">Mathematical intervals</text>
                        <text x="225" y="210" text-anchor="middle" font-size="11" fill="white">[start, end], [start, ∞), etc.</text>

                        <!-- SequenceIndex Layer -->
                        <rect x="420" y="150" width="330" height="80" fill="#27ae60" stroke="#229954" stroke-width="2" rx="8"/>
                        <text x="585" y="175" text-anchor="middle" font-size="14" font-weight="bold" fill="white">SequenceIndex</text>
                        <text x="585" y="195" text-anchor="middle" font-size="11" fill="white">Safe sequence indexing</text>
                        <text x="585" y="210" text-anchor="middle" font-size="11" fill="white">1-based → 0-based conversion</text>

                        <!-- Region Layer -->
                        <rect x="50" y="250" width="700" height="80" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="8"/>
                        <text x="400" y="275" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Region (Genomic Regions)</text>
                        <text x="400" y="295" text-anchor="middle" font-size="12" fill="white">Reference sequence name + Interval</text>
                        <text x="400" y="315" text-anchor="middle" font-size="12" fill="white">chr1:1000-2000, chr2:500, etc.</text>

                        <!-- Arrows -->
                        <defs>
                            <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                            </marker>
                        </defs>

                        <!-- Dependency arrows -->
                        <line x1="225" y1="130" x2="225" y2="145" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
                        <line x1="585" y1="130" x2="585" y2="145" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
                        <line x1="400" y1="230" x2="400" y2="245" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>

                        <!-- Labels -->
                        <text x="150" y="140" font-size="10" fill="#7f8c8d">uses</text>
                        <text x="510" y="140" font-size="10" fill="#7f8c8d">uses</text>
                        <text x="320" y="240" font-size="10" fill="#7f8c8d">contains</text>

                        <!-- Feature boxes -->
                        <rect x="50" y="350" width="160" height="30" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="4"/>
                        <text x="130" y="368" text-anchor="middle" font-size="10" fill="white">Type Safety</text>

                        <rect x="230" y="350" width="160" height="30" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="4"/>
                        <text x="310" y="368" text-anchor="middle" font-size="10" fill="white">Zero-cost Abstractions</text>

                        <rect x="410" y="350" width="160" height="30" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="4"/>
                        <text x="490" y="368" text-anchor="middle" font-size="10" fill="white">Mathematical Correctness</text>

                        <rect x="590" y="350" width="160" height="30" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="4"/>
                        <text x="670" y="368" text-anchor="middle" font-size="10" fill="white">Memory Safety</text>
                    </svg>
                </div>

                <h3>Dependencies</h3>
                <p>Noodles core has minimal dependencies to ensure fast compilation and broad compatibility:</p>
                <ul>
                    <li><code>bstr</code> - Byte string handling for sequence names</li>
                    <li><code>std</code> - Standard library types (NonZeroUsize, ranges, etc.)</li>
                </ul>

                <div class="info">
                    <h4>💡 Design Philosophy</h4>
                    <p>The library follows Rust's zero-cost abstractions principle. All the type safety and mathematical correctness comes with no runtime overhead - the compiled code is as efficient as using raw integers, but with compile-time guarantees of correctness.</p>
                </div>

                <h3>Integration with Noodles Ecosystem</h3>
                <p>Every noodles library depends on noodles-core:</p>
                <ul>
                    <li><code>noodles-gff</code> - Uses Position for feature coordinates</li>
                    <li><code>noodles-gtf</code> - Uses Position for gene/transcript positions</li>
                    <li><code>noodles-sam</code> - Uses Position for alignment coordinates</li>
                    <li><code>noodles-vcf</code> - Uses Position for variant positions</li>
                    <li><code>noodles-bed</code> - Converts between 0-based and 1-based systems</li>
                    <li><code>noodles-csi</code> - Uses Region for genomic queries</li>
                </ul>
            </section>

            <!-- Position Section -->
            <section id="position" class="section">
                <h2>📍 Position Type - 1-based Coordinates</h2>

                <h3>Understanding the Position Type</h3>
                <p>The <code>Position</code> type is a wrapper around <code>NonZeroUsize</code> that represents 1-based genomic coordinates:</p>

                <div class="api-method">
                    <div class="method-signature">pub struct Position(NonZeroUsize)</div>
                    <p>Key characteristics:</p>
                    <ul>
                        <li>Always represents a valid 1-based position (≥ 1)</li>
                        <li>Cannot be zero (compile-time guarantee)</li>
                        <li>Zero-cost abstraction over NonZeroUsize</li>
                        <li>Implements Copy, Clone, Debug, Eq, Hash, Ord</li>
                    </ul>
                </div>

                <h3>Creating Positions</h3>
                <div class="code-block">
use noodles_core::Position;

// Method 1: Using new() - returns Option
let pos1 = Position::new(1000);        // Some(Position(1000))
let pos2 = Position::new(0);           // None - zero is invalid

// Method 2: Using try_from() - returns Result
let pos3 = Position::try_from(1000)?;  // Ok(Position(1000))
let pos4 = Position::try_from(0);      // Err(TryFromIntError)

// Method 3: Using constants
let min_pos = Position::MIN;           // Position(1)
let max_pos = Position::MAX;           // Position(usize::MAX)

// Method 4: Parsing from strings
let pos5: Position = "1000".parse()?;  // Ok(Position(1000))
                </div>

                <h3>Position Operations</h3>
                <div class="code-block">
// Getting the underlying value
let pos = Position::try_from(1000)?;
let value: usize = pos.get();          // 1000
let value2: usize = usize::from(pos);  // 1000 (conversion)

// Arithmetic operations
let pos1 = Position::try_from(100)?;
let pos2 = pos1.checked_add(50);       // Some(Position(150))
let pos3 = pos1.checked_add(usize::MAX); // None (overflow)

// Comparison operations
let a = Position::try_from(100)?;
let b = Position::try_from(200)?;
assert!(a < b);
assert!(a <= b);
assert!(b > a);
assert!(b >= a);
assert!(a != b);
                </div>

                <div class="math-formula">
                    <h4>Position Arithmetic</h4>
                    <p>For a position $p$ and offset $n$:</p>
                    <p>$$\text{checked\_add}(p, n) = \begin{cases}
                    \text{Some}(p + n) & \text{if } p + n \leq \text{usize::MAX} \\
                    \text{None} & \text{if overflow}
                    \end{cases}$$</p>
                </div>

                <h3>Display and Formatting</h3>
                <div class="code-block">
let pos = Position::try_from(1000)?;

// Display formatting
println!("{}", pos);           // "1000"
println!("{:?}", pos);         // "Position(1000)"

// String conversion
let s = pos.to_string();       // "1000"
let parsed: Position = s.parse()?; // Back to Position(1000)
                </div>

                <div class="highlight">
                    <h4>🎯 Key Benefits</h4>
                    <ul>
                        <li><strong>Type Safety</strong> - Cannot accidentally use zero as a position</li>
                        <li><strong>Self-Documenting</strong> - Code clearly shows 1-based coordinates</li>
                        <li><strong>Optimization</strong> - Compiler can optimize knowing values are non-zero</li>
                        <li><strong>Interoperability</strong> - Easy conversion to/from usize when needed</li>
                    </ul>
                </div>
            </section>

            <!-- Sequence Indexing Section -->
            <section id="sequence-indexing" class="section">
                <h2>🔍 Sequence Indexing</h2>

                <h3>The SequenceIndex Trait</h3>
                <p>The <code>SequenceIndex</code> trait provides safe indexing into sequences using 1-based positions:</p>

                <div class="api-method">
                    <div class="method-signature">pub trait SequenceIndex&lt;T&gt;</div>
                    <p>Core methods:</p>
                    <ul>
                        <li><code>get(self, sequence: &[T]) -> Option&lt;&Self::Output&gt;</code> - Safe access</li>
                        <li><code>get_mut(self, sequence: &mut [T]) -> Option&lt;&mut Self::Output&gt;</code> - Safe mutable access</li>
                        <li><code>index(self, sequence: &[T]) -> &Self::Output</code> - Panicking access</li>
                        <li><code>index_mut(self, sequence: &mut [T]) -> &mut Self::Output</code> - Panicking mutable access</li>
                    </ul>
                </div>

                <h3>Single Position Indexing</h3>
                <div class="code-block">
use noodles_core::{Position, position::SequenceIndex};

let sequence = b"ATCGATCG";
let pos = Position::try_from(3)?;  // 3rd position (1-based)

// Safe access - returns Option
if let Some(base) = pos.get(sequence) {
    println!("Base at position {}: {}", pos, *base as char); // "Base at position 3: C"
}

// Direct access - panics if out of bounds
let base = pos.index(sequence);
println!("Base: {}", *base as char); // "Base: C"

// The conversion: 1-based position 3 -> 0-based index 2
// sequence[2] = 'C'
//   A T C G A T C G
//   1 2 3 4 5 6 7 8  (1-based positions)
//   0 1 2 3 4 5 6 7  (0-based indices)
                </div>

                <h3>Range Indexing</h3>
                <p>The trait is implemented for various range types:</p>

                <div class="code-block">
let sequence = b"ATCGATCGATCG";
let start = Position::try_from(3)?;
let end = Position::try_from(6)?;

// Inclusive range [start, end]
let range = start..=end;
if let Some(subseq) = range.get(sequence) {
    println!("Subsequence: {}", std::str::from_utf8(subseq)?); // "CGAT"
}

// Half-open range [start, end)
let range = start..end;
if let Some(subseq) = range.get(sequence) {
    println!("Subsequence: {}", std::str::from_utf8(subseq)?); // "CGA"
}

// From position to end
let range = start..;
if let Some(subseq) = range.get(sequence) {
    println!("From position 3: {}", std::str::from_utf8(subseq)?); // "CGATCGATCG"
}

// Up to position (exclusive)
let range = ..end;
if let Some(subseq) = range.get(sequence) {
    println!("Up to position 6: {}", std::str::from_utf8(subseq)?); // "ATCGA"
}

// Up to position (inclusive)
let range = ..=end;
if let Some(subseq) = range.get(sequence) {
    println!("Up to position 6 (incl): {}", std::str::from_utf8(subseq)?); // "ATCGAT"
}
                </div>

                <div class="math-formula">
                    <h4>Index Conversion Formula</h4>
                    <p>For 1-based position $p$ to 0-based index $i$:</p>
                    <p>$$i = p - 1$$</p>
                    <p>For range $[p_1, p_2]$ (1-based) to slice $[i_1, i_2)$ (0-based):</p>
                    <p>$$i_1 = p_1 - 1, \quad i_2 = p_2$$</p>
                </div>

                <h3>Mutable Indexing</h3>
                <div class="code-block">
let mut sequence = b"ATCGATCG".to_vec();
let pos = Position::try_from(3)?;

// Mutable access to single position
if let Some(base) = pos.get_mut(&mut sequence) {
    *base = b'T';  // Change C to T
}
println!("Modified: {}", std::str::from_utf8(&sequence)?); // "ATTGATCG"

// Mutable access to range
let range = Position::try_from(1)?..=Position::try_from(3)?;
if let Some(subseq) = range.get_mut(&mut sequence) {
    subseq.copy_from_slice(b"GGG");
}
println!("Modified: {}", std::str::from_utf8(&sequence)?); // "GGGGATCG"
                </div>

                <div class="warning">
                    <h4>⚠️ Bounds Checking</h4>
                    <p>The <code>get</code> methods return <code>None</code> if the position/range is out of bounds. The <code>index</code> methods panic. Always prefer <code>get</code> methods for safe access unless you're certain the position is valid.</p>
                </div>
            </section>

            <!-- Intervals Section -->
            <section id="intervals" class="section">
                <h2>📏 Intervals and Ranges</h2>

                <h3>Understanding Intervals</h3>
                <p>The <code>Interval</code> type represents mathematical intervals that can be bounded or unbounded:</p>

                <div class="api-method">
                    <div class="method-signature">pub struct Interval { start: Option&lt;Position&gt;, end: Option&lt;Position&gt; }</div>
                    <p>Interval types:</p>
                    <ul>
                        <li><strong>Closed</strong> - [a, b] (both bounds included)</li>
                        <li><strong>Left-unbounded</strong> - (-∞, b] (no start, end included)</li>
                        <li><strong>Right-unbounded</strong> - [a, ∞) (start included, no end)</li>
                        <li><strong>Unbounded</strong> - (-∞, ∞) (no bounds)</li>
                    </ul>
                </div>

                <h3>Creating Intervals</h3>
                <div class="code-block">
use noodles_core::{Position, region::Interval};

let start = Position::try_from(100)?;
let end = Position::try_from(200)?;

// Closed interval [100, 200]
let interval1 = Interval::from(start..=end);

// Right-unbounded interval [100, ∞)
let interval2 = Interval::from(start..);

// Left-unbounded interval (-∞, 200]
let interval3 = Interval::from(..=end);

// Unbounded interval (-∞, ∞)
let interval4 = Interval::from(..);

// Access interval bounds
println!("Start: {:?}", interval1.start()); // Some(Position(100))
println!("End: {:?}", interval1.end());     // Some(Position(200))
                </div>

                <h3>Interval Operations</h3>
                <div class="code-block">
let interval = Interval::from(Position::try_from(100)?..=Position::try_from(200)?);

// Check if a position is contained
let pos1 = Position::try_from(150)?;
let pos2 = Position::try_from(250)?;

assert!(interval.contains(pos1));  // true - 150 is in [100, 200]
assert!(!interval.contains(pos2)); // false - 250 is not in [100, 200]

// Check if intervals intersect
let other = Interval::from(Position::try_from(150)?..=Position::try_from(300)?);
assert!(interval.intersects(other)); // true - [100, 200] ∩ [150, 300] = [150, 200]

let disjoint = Interval::from(Position::try_from(300)?..=Position::try_from(400)?);
assert!(!interval.intersects(disjoint)); // false - no overlap
                </div>

                <div style="text-align: center; margin: 2rem 0;">
                    <svg width="700" height="300" viewBox="0 0 700 300" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="700" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                        <!-- Title -->
                        <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Interval Types and Operations</text>

                        <!-- Number line -->
                        <line x1="50" y1="80" x2="650" y2="80" stroke="#2c3e50" stroke-width="2"/>

                        <!-- Tick marks -->
                        <line x1="100" y1="75" x2="100" y2="85" stroke="#2c3e50" stroke-width="1"/>
                        <text x="100" y="100" text-anchor="middle" font-size="10" fill="#2c3e50">100</text>

                        <line x1="200" y1="75" x2="200" y2="85" stroke="#2c3e50" stroke-width="1"/>
                        <text x="200" y="100" text-anchor="middle" font-size="10" fill="#2c3e50">200</text>

                        <line x1="300" y1="75" x2="300" y2="85" stroke="#2c3e50" stroke-width="1"/>
                        <text x="300" y="100" text-anchor="middle" font-size="10" fill="#2c3e50">300</text>

                        <line x1="400" y1="75" x2="400" y2="85" stroke="#2c3e50" stroke-width="1"/>
                        <text x="400" y="100" text-anchor="middle" font-size="10" fill="#2c3e50">400</text>

                        <line x1="500" y1="75" x2="500" y2="85" stroke="#2c3e50" stroke-width="1"/>
                        <text x="500" y="100" text-anchor="middle" font-size="10" fill="#2c3e50">500</text>

                        <!-- Interval A: [100, 300] -->
                        <line x1="100" y1="130" x2="300" y2="130" stroke="#3498db" stroke-width="6"/>
                        <circle cx="100" cy="130" r="4" fill="#3498db"/>
                        <circle cx="300" cy="130" r="4" fill="#3498db"/>
                        <text x="200" y="150" text-anchor="middle" font-size="12" fill="#3498db">A: [100, 300]</text>

                        <!-- Interval B: [200, 500] -->
                        <line x1="200" y1="170" x2="500" y2="170" stroke="#e74c3c" stroke-width="6"/>
                        <circle cx="200" cy="170" r="4" fill="#e74c3c"/>
                        <circle cx="500" cy="170" r="4" fill="#e74c3c"/>
                        <text x="350" y="190" text-anchor="middle" font-size="12" fill="#e74c3c">B: [200, 500]</text>

                        <!-- Intersection -->
                        <line x1="200" y1="210" x2="300" y2="210" stroke="#27ae60" stroke-width="8"/>
                        <circle cx="200" cy="210" r="4" fill="#27ae60"/>
                        <circle cx="300" cy="210" r="4" fill="#27ae60"/>
                        <text x="250" y="230" text-anchor="middle" font-size="12" fill="#27ae60">A ∩ B: [200, 300]</text>

                        <!-- Unbounded interval [200, ∞) -->
                        <line x1="200" y1="250" x2="620" y2="250" stroke="#f39c12" stroke-width="6"/>
                        <circle cx="200" cy="250" r="4" fill="#f39c12"/>
                        <polygon points="620,245 630,250 620,255" fill="#f39c12"/>
                        <text x="400" y="270" text-anchor="middle" font-size="12" fill="#f39c12">C: [200, ∞)</text>
                    </svg>
                </div>

                <h3>Interval Arithmetic</h3>
                <div class="math-formula">
                    <h4>Intersection Formula</h4>
                    <p>For intervals $A = [a_1, a_2]$ and $B = [b_1, b_2]$:</p>
                    <p>$$A \cap B \neq \emptyset \iff \max(a_1, b_1) \leq \min(a_2, b_2)$$</p>
                    <p>The intersection is: $[\max(a_1, b_1), \min(a_2, b_2)]$</p>
                </div>

                <h3>Parsing Intervals</h3>
                <div class="code-block">
// Parse interval from string
let interval1: Interval = "100-200".parse()?;  // [100, 200]
let interval2: Interval = "100".parse()?;      // [100, ∞)
let interval3: Interval = "".parse()?;         // (-∞, ∞)

// Display intervals
println!("{}", interval1); // "100-200"
println!("{}", interval2); // "100"
println!("{}", interval3); // ""
                </div>
            </section>

            <!-- Regions Section -->
            <section id="regions" class="section">
                <h2>🗺️ Genomic Regions</h2>

                <h3>Understanding Genomic Regions</h3>
                <p>A <code>Region</code> combines a reference sequence name with an interval to represent a genomic location:</p>

                <div class="api-method">
                    <div class="method-signature">pub struct Region { name: BString, interval: Interval }</div>
                    <p>Components:</p>
                    <ul>
                        <li><strong>name</strong> - Reference sequence identifier (chromosome, contig, etc.)</li>
                        <li><strong>interval</strong> - Position range on that sequence</li>
                    </ul>
                </div>

                <h3>Creating Regions</h3>
                <div class="code-block">
use noodles_core::{Position, Region};

let start = Position::try_from(1000)?;
let end = Position::try_from(2000)?;

// Various ways to create regions
let region1 = Region::new("chr1", start..=end);        // chr1:1000-2000
let region2 = Region::new("chr2", start..);            // chr2:1000
let region3 = Region::new("chr3", ..=end);             // chr3:1-2000
let region4 = Region::new("chr4", ..);                 // chr4

// Access region components
println!("Name: {}", region1.name());                  // "chr1"
println!("Start: {:?}", region1.start());              // Bound::Included(Position(1000))
println!("End: {:?}", region1.end());                  // Bound::Included(Position(2000))
println!("Interval: {}", region1.interval());          // "1000-2000"
                </div>

                <h3>Region Parsing</h3>
                <p>Regions can be parsed from standard genomic coordinate strings:</p>

                <div class="code-block">
// Parse various region formats
let region1: Region = "chr1".parse()?;                 // Entire chromosome
let region2: Region = "chr1:1000".parse()?;            // From position 1000 to end
let region3: Region = "chr1:1000-2000".parse()?;       // Specific range
let region4: Region = "scaffold_123:500-1500".parse()?; // Scaffold with range

// Display regions
println!("{}", region1); // "chr1"
println!("{}", region2); // "chr1:1000"
println!("{}", region3); // "chr1:1000-2000"
println!("{}", region4); // "scaffold_123:500-1500"
                </div>

                <h3>Working with Region Bounds</h3>
                <div class="code-block">
use std::ops::Bound;

let region = Region::new("chr1", Position::try_from(1000)?..=Position::try_from(2000)?);

// Extract bounds for processing
match (region.start(), region.end()) {
    (Bound::Included(start), Bound::Included(end)) => {
        println!("Closed interval: {} to {}", start, end);
        let length = usize::from(end) - usize::from(start) + 1;
        println!("Length: {} bp", length);
    }
    (Bound::Included(start), Bound::Unbounded) => {
        println!("From position {} to end of sequence", start);
    }
    (Bound::Unbounded, Bound::Included(end)) => {
        println!("From start of sequence to position {}", end);
    }
    (Bound::Unbounded, Bound::Unbounded) => {
        println!("Entire sequence");
    }
    _ => {} // Other bound types
}
                </div>

                <div class="highlight">
                    <h4>🎯 Common Use Cases</h4>
                    <ul>
                        <li><strong>Genomic Queries</strong> - Specify regions for data retrieval</li>
                        <li><strong>Feature Annotation</strong> - Define where genomic features are located</li>
                        <li><strong>Variant Calling</strong> - Specify regions for variant analysis</li>
                        <li><strong>Alignment Filtering</strong> - Filter reads by genomic location</li>
                        <li><strong>Index Queries</strong> - Query indexed genomic data files</li>
                    </ul>
                </div>

                <h3>Region Validation</h3>
                <div class="code-block">
// Validate region coordinates
fn validate_region(region: &Region) -> Result<(), String> {
    match (region.start(), region.end()) {
        (Bound::Included(start), Bound::Included(end)) => {
            if start > end {
                return Err(format!("Invalid region: start {} > end {}", start, end));
            }
        }
        _ => {} // Unbounded regions are always valid
    }

    // Check sequence name
    if region.name().is_empty() {
        return Err("Empty sequence name".to_string());
    }

    Ok(())
}

// Example usage
let valid_region = Region::new("chr1", Position::try_from(100)?..=Position::try_from(200)?);
let invalid_region = Region::new("chr1", Position::try_from(200)?..=Position::try_from(100)?);

assert!(validate_region(&valid_region).is_ok());
assert!(validate_region(&invalid_region).is_err());
                </div>
            </section>

            <!-- Coordinate Systems Section -->
            <section id="coordinate-systems" class="section">
                <h2>📐 Coordinate Systems</h2>

                <h3>1-based vs 0-based Systems</h3>
                <p>Understanding coordinate systems is crucial for genomic data processing:</p>

                <div style="text-align: center; margin: 2rem 0;">
                    <svg width="800" height="250" viewBox="0 0 800 250" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="800" height="250" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Coordinate System Comparison</text>

                        <!-- Sequence -->
                        <text x="50" y="60" font-size="14" font-weight="bold" fill="#2c3e50">Sequence:</text>

                        <!-- Bases -->
                        <rect x="150" y="45" width="30" height="30" fill="#3498db" stroke="#2980b9" stroke-width="1"/>
                        <text x="165" y="65" text-anchor="middle" font-size="14" fill="white">A</text>

                        <rect x="180" y="45" width="30" height="30" fill="#3498db" stroke="#2980b9" stroke-width="1"/>
                        <text x="195" y="65" text-anchor="middle" font-size="14" fill="white">T</text>

                        <rect x="210" y="45" width="30" height="30" fill="#3498db" stroke="#2980b9" stroke-width="1"/>
                        <text x="225" y="65" text-anchor="middle" font-size="14" fill="white">C</text>

                        <rect x="240" y="45" width="30" height="30" fill="#3498db" stroke="#2980b9" stroke-width="1"/>
                        <text x="255" y="65" text-anchor="middle" font-size="14" fill="white">G</text>

                        <rect x="270" y="45" width="30" height="30" fill="#3498db" stroke="#2980b9" stroke-width="1"/>
                        <text x="285" y="65" text-anchor="middle" font-size="14" fill="white">A</text>

                        <rect x="300" y="45" width="30" height="30" fill="#3498db" stroke="#2980b9" stroke-width="1"/>
                        <text x="315" y="65" text-anchor="middle" font-size="14" fill="white">T</text>

                        <!-- 1-based positions -->
                        <text x="50" y="110" font-size="14" font-weight="bold" fill="#e67e22">1-based:</text>
                        <text x="165" y="110" text-anchor="middle" font-size="12" fill="#e67e22">1</text>
                        <text x="195" y="110" text-anchor="middle" font-size="12" fill="#e67e22">2</text>
                        <text x="225" y="110" text-anchor="middle" font-size="12" fill="#e67e22">3</text>
                        <text x="255" y="110" text-anchor="middle" font-size="12" fill="#e67e22">4</text>
                        <text x="285" y="110" text-anchor="middle" font-size="12" fill="#e67e22">5</text>
                        <text x="315" y="110" text-anchor="middle" font-size="12" fill="#e67e22">6</text>

                        <!-- 0-based indices -->
                        <text x="50" y="140" font-size="14" font-weight="bold" fill="#27ae60">0-based:</text>
                        <text x="165" y="140" text-anchor="middle" font-size="12" fill="#27ae60">0</text>
                        <text x="195" y="140" text-anchor="middle" font-size="12" fill="#27ae60">1</text>
                        <text x="225" y="140" text-anchor="middle" font-size="12" fill="#27ae60">2</text>
                        <text x="255" y="140" text-anchor="middle" font-size="12" fill="#27ae60">3</text>
                        <text x="285" y="140" text-anchor="middle" font-size="12" fill="#27ae60">4</text>
                        <text x="315" y="140" text-anchor="middle" font-size="12" fill="#27ae60">5</text>

                        <!-- Range example -->
                        <text x="400" y="110" font-size="12" fill="#2c3e50">Range [2, 4] (1-based)</text>
                        <line x1="180" y1="85" x2="270" y2="85" stroke="#e74c3c" stroke-width="3"/>
                        <circle cx="180" cy="85" r="3" fill="#e74c3c"/>
                        <circle cx="270" cy="85" r="3" fill="#e74c3c"/>

                        <text x="400" y="140" font-size="12" fill="#2c3e50">Range [1, 4) (0-based)</text>
                        <line x1="180" y1="155" x2="270" y2="155" stroke="#27ae60" stroke-width="3"/>
                        <circle cx="180" cy="155" r="3" fill="#27ae60"/>
                        <circle cx="270" cy="155" r="3" fill="white" stroke="#27ae60" stroke-width="2"/>

                        <!-- Conversion formula -->
                        <rect x="450" y="170" width="300" height="60" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1" rx="5"/>
                        <text x="600" y="190" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Conversion Formula</text>
                        <text x="600" y="210" text-anchor="middle" font-size="11" fill="#2c3e50">1-based position p → 0-based index (p - 1)</text>
                        <text x="600" y="225" text-anchor="middle" font-size="11" fill="#2c3e50">0-based index i → 1-based position (i + 1)</text>
                    </svg>
                </div>

                <h3>Safe Coordinate Conversion</h3>
                <div class="code-block">
use noodles_core::Position;

// Convert 1-based position to 0-based index
let pos = Position::try_from(5)?;
let index = usize::from(pos) - 1;  // 4 (0-based)

// Convert 0-based index to 1-based position
let index = 4;
let pos = Position::try_from(index + 1)?;  // Position(5)

// Safe conversion function
fn to_zero_based(pos: Position) -> usize {
    usize::from(pos) - 1
}

fn from_zero_based(index: usize) -> Result<Position, noodles_core::position::TryFromIntError> {
    Position::try_from(index + 1)
}

// Example usage
let one_based = Position::try_from(100)?;
let zero_based = to_zero_based(one_based);  // 99
let back_to_one = from_zero_based(zero_based)?;  // Position(100)
                </div>

                <h3>Format-Specific Conventions</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>1-based Formats</h4>
                        <ul>
                            <li><strong>GFF3/GTF</strong> - Feature positions</li>
                            <li><strong>SAM/BAM</strong> - Alignment positions</li>
                            <li><strong>VCF</strong> - Variant positions</li>
                            <li><strong>FASTA</strong> - Sequence positions</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>0-based Formats</h4>
                        <ul>
                            <li><strong>BED</strong> - Feature coordinates</li>
                            <li><strong>Programming arrays</strong> - Internal indexing</li>
                            <li><strong>Some APIs</strong> - Library interfaces</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Mathematical Operations Section -->
            <section id="mathematical-operations" class="section">
                <h2>🧮 Mathematical Operations</h2>

                <h3>Position Arithmetic</h3>
                <div class="code-block">
use noodles_core::Position;

let pos = Position::try_from(100)?;

// Safe addition
let result1 = pos.checked_add(50);        // Some(Position(150))
let result2 = pos.checked_add(usize::MAX); // None (overflow)

// Manual arithmetic (requires conversion)
let value = usize::from(pos);
let new_value = value + 50;
let new_pos = Position::try_from(new_value)?;

// Distance calculation
let pos1 = Position::try_from(100)?;
let pos2 = Position::try_from(200)?;
let distance = usize::from(pos2) - usize::from(pos1); // 100
                </div>

                <h3>Interval Length Calculation</h3>
                <div class="math-formula">
                    <h4>Interval Length Formula</h4>
                    <p>For a closed interval $[a, b]$ (1-based, inclusive):</p>
                    <p>$$\text{length} = b - a + 1$$</p>
                    <p>For a half-open interval $[a, b)$ (0-based):</p>
                    <p>$$\text{length} = b - a$$</p>
                </div>

                <div class="code-block">
use std::ops::Bound;

// Calculate length of a region
fn region_length(region: &Region) -> Option<usize> {
    match (region.start(), region.end()) {
        (Bound::Included(start), Bound::Included(end)) => {
            Some(usize::from(end) - usize::from(start) + 1)
        }
        _ => None, // Unbounded regions have infinite length
    }
}

// Example
let region = Region::new("chr1", Position::try_from(100)?..=Position::try_from(200)?);
let length = region_length(&region).unwrap(); // 101 bp
                </div>

                <h3>Overlap Calculations</h3>
                <div class="code-block">
// Calculate overlap between two intervals
fn calculate_overlap(a: Interval, b: Interval) -> Option<usize> {
    if !a.intersects(b) {
        return Some(0);
    }

    let a_start = a.start().unwrap_or(Position::MIN);
    let a_end = a.end().unwrap_or(Position::MAX);
    let b_start = b.start().unwrap_or(Position::MIN);
    let b_end = b.end().unwrap_or(Position::MAX);

    let overlap_start = std::cmp::max(a_start, b_start);
    let overlap_end = std::cmp::min(a_end, b_end);

    Some(usize::from(overlap_end) - usize::from(overlap_start) + 1)
}

// Example
let interval1 = Interval::from(Position::try_from(100)?..=Position::try_from(200)?);
let interval2 = Interval::from(Position::try_from(150)?..=Position::try_from(250)?);
let overlap = calculate_overlap(interval1, interval2).unwrap(); // 51 bp
                </div>
            </section>

            <!-- Parsing Section -->
            <section id="parsing" class="section">
                <h2>📝 Parsing and String Conversion</h2>

                <h3>Position Parsing</h3>
                <div class="code-block">
use noodles_core::Position;

// Parse from string
let pos1: Position = "1000".parse()?;
let pos2 = Position::from_str("2000")?;

// Handle parsing errors
match "0".parse::<Position>() {
    Ok(pos) => println!("Position: {}", pos),
    Err(e) => println!("Parse error: {}", e), // "invalid digit found in string"
}

match "abc".parse::<Position>() {
    Ok(pos) => println!("Position: {}", pos),
    Err(e) => println!("Parse error: {}", e), // "invalid digit found in string"
}

// Convert to string
let pos = Position::try_from(1000)?;
let s1 = pos.to_string();     // "1000"
let s2 = format!("{}", pos);  // "1000"
                </div>

                <h3>Region Parsing</h3>
                <div class="code-block">
use noodles_core::Region;

// Parse various region formats
let examples = vec![
    "chr1",           // Entire chromosome
    "chr1:",          // Entire chromosome (explicit)
    "chr1:1000",      // From position 1000 to end
    "chr1:1000-2000", // Specific range
    "scaffold_123:500-1500", // Scaffold with range
];

for example in examples {
    match example.parse::<Region>() {
        Ok(region) => println!("{} -> {}", example, region),
        Err(e) => println!("{} -> Error: {}", example, e),
    }
}

// Handle parsing errors
match "".parse::<Region>() {
    Ok(region) => println!("Region: {}", region),
    Err(e) => println!("Parse error: {}", e), // "empty input"
}

match "chr1:abc".parse::<Region>() {
    Ok(region) => println!("Region: {}", region),
    Err(e) => println!("Parse error: {}", e), // "invalid interval"
}
                </div>

                <h3>Custom Parsing Functions</h3>
                <div class="code-block">
// Parse region with validation
fn parse_validated_region(s: &str) -> Result<Region, Box<dyn std::error::Error>> {
    let region: Region = s.parse()?;

    // Validate sequence name
    if region.name().is_empty() {
        return Err("Empty sequence name".into());
    }

    // Validate coordinates
    match (region.start(), region.end()) {
        (Bound::Included(start), Bound::Included(end)) => {
            if start > end {
                return Err(format!("Invalid coordinates: {} > {}", start, end).into());
            }
        }
        _ => {} // Unbounded regions are valid
    }

    Ok(region)
}

// Parse multiple regions
fn parse_region_list(s: &str) -> Result<Vec<Region>, Box<dyn std::error::Error>> {
    s.split(',')
        .map(|part| part.trim().parse())
        .collect()
}

// Example usage
let regions = parse_region_list("chr1:1000-2000, chr2:500, chr3")?;
for region in regions {
    println!("Parsed: {}", region);
}
                </div>
            </section>

            <!-- Error Handling Section -->
            <section id="error-handling" class="section">
                <h2>⚠️ Error Handling</h2>

                <h3>Position Errors</h3>
                <div class="code-block">
use noodles_core::{Position, position};

// TryFromIntError when converting from usize
match Position::try_from(0) {
    Ok(pos) => println!("Position: {}", pos),
    Err(e) => {
        println!("Error: {}", e); // "out of range integral type conversion attempted"
        assert_eq!(e, position::TryFromIntError::default());
    }
}

// ParseError when parsing from string
match "0".parse::<Position>() {
    Ok(pos) => println!("Position: {}", pos),
    Err(e) => {
        println!("Parse error: {}", e);
        // This is a num::ParseIntError
    }
}

// Safe creation patterns
fn safe_position_from_usize(n: usize) -> Option<Position> {
    Position::new(n)
}

fn safe_position_from_string(s: &str) -> Result<Position, position::ParseError> {
    s.parse()
}
                </div>

                <h3>Region Parsing Errors</h3>
                <div class="code-block">
use noodles_core::{Region, region::ParseError};

// Handle different parsing errors
fn parse_region_with_details(s: &str) -> Result<Region, String> {
    match s.parse::<Region>() {
        Ok(region) => Ok(region),
        Err(ParseError::Empty) => Err("Input string is empty".to_string()),
        Err(ParseError::Ambiguous) => Err("Input is ambiguous".to_string()),
        Err(ParseError::Invalid) => Err("Input format is invalid".to_string()),
        Err(ParseError::InvalidInterval(e)) => {
            Err(format!("Invalid interval: {}", e))
        }
    }
}

// Example error handling
let test_cases = vec!["", "chr1:abc", "chr1:100-abc"];
for case in test_cases {
    match parse_region_with_details(case) {
        Ok(region) => println!("{} -> {}", case, region),
        Err(e) => println!("{} -> Error: {}", case, e),
    }
}
                </div>

                <h3>Robust Error Handling Patterns</h3>
                <div class="code-block">
// Comprehensive error handling for genomic operations
#[derive(Debug)]
enum GenomicError {
    InvalidPosition(position::TryFromIntError),
    InvalidRegion(region::ParseError),
    CoordinateOverflow,
    InvalidRange { start: Position, end: Position },
}

impl std::fmt::Display for GenomicError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::InvalidPosition(e) => write!(f, "Invalid position: {}", e),
            Self::InvalidRegion(e) => write!(f, "Invalid region: {}", e),
            Self::CoordinateOverflow => write!(f, "Coordinate calculation overflow"),
            Self::InvalidRange { start, end } => {
                write!(f, "Invalid range: start {} > end {}", start, end)
            }
        }
    }
}

impl std::error::Error for GenomicError {}

// Safe genomic operations
fn safe_create_region(name: &str, start: usize, end: usize) -> Result<Region, GenomicError> {
    let start_pos = Position::try_from(start).map_err(GenomicError::InvalidPosition)?;
    let end_pos = Position::try_from(end).map_err(GenomicError::InvalidPosition)?;

    if start_pos > end_pos {
        return Err(GenomicError::InvalidRange { start: start_pos, end: end_pos });
    }

    Ok(Region::new(name, start_pos..=end_pos))
}
                </div>
            </section>

            <!-- Examples Section -->
            <section id="examples" class="section">
                <h2>💡 Practical Examples</h2>

                <h3>Example 1: Genomic Feature Overlap</h3>
                <div class="code-block">
use noodles_core::{Position, Region, region::Interval};
use std::collections::HashMap;

#[derive(Debug)]
struct GenomicFeature {
    name: String,
    region: Region,
    feature_type: String,
}

impl GenomicFeature {
    fn new(name: &str, chr: &str, start: usize, end: usize, feature_type: &str) -> Result<Self, Box<dyn std::error::Error>> {
        let start_pos = Position::try_from(start)?;
        let end_pos = Position::try_from(end)?;
        let region = Region::new(chr, start_pos..=end_pos);

        Ok(Self {
            name: name.to_string(),
            region,
            feature_type: feature_type.to_string(),
        })
    }

    fn overlaps_with(&self, other: &Self) -> bool {
        // Only check overlap if on same chromosome
        if self.region.name() != other.region.name() {
            return false;
        }

        self.region.interval().intersects(other.region.interval())
    }

    fn overlap_length(&self, other: &Self) -> Option<usize> {
        if !self.overlaps_with(other) {
            return Some(0);
        }

        use std::ops::Bound;

        let (self_start, self_end) = match (self.region.start(), self.region.end()) {
            (Bound::Included(s), Bound::Included(e)) => (s, e),
            _ => return None, // Can't calculate for unbounded regions
        };

        let (other_start, other_end) = match (other.region.start(), other.region.end()) {
            (Bound::Included(s), Bound::Included(e)) => (s, e),
            _ => return None,
        };

        let overlap_start = std::cmp::max(self_start, other_start);
        let overlap_end = std::cmp::min(self_end, other_end);

        Some(usize::from(overlap_end) - usize::from(overlap_start) + 1)
    }
}

// Example usage
fn main() -> Result<(), Box<dyn std::error::Error>> {
    let gene = GenomicFeature::new("BRCA1", "chr17", 43044295, 43125483, "gene")?;
    let exon = GenomicFeature::new("BRCA1_exon1", "chr17", 43044295, 43044394, "exon")?;
    let variant = GenomicFeature::new("rs80357382", "chr17", 43045677, 43045677, "SNV")?;

    println!("Gene: {} at {}", gene.name, gene.region);
    println!("Exon: {} at {}", exon.name, exon.region);
    println!("Variant: {} at {}", variant.name, variant.region);

    println!("Gene overlaps exon: {}", gene.overlaps_with(&exon));
    println!("Gene overlaps variant: {}", gene.overlaps_with(&variant));
    println!("Overlap length (gene-exon): {:?} bp", gene.overlap_length(&exon));

    Ok(())
}
                </div>

                <h3>Example 2: Genomic Window Analysis</h3>
                <div class="code-block">
// Sliding window analysis over genomic regions
struct GenomicWindow {
    region: Region,
    window_size: usize,
    step_size: usize,
}

impl GenomicWindow {
    fn new(region: Region, window_size: usize, step_size: usize) -> Self {
        Self { region, window_size, step_size }
    }

    fn windows(&self) -> Result<Vec<Region>, Box<dyn std::error::Error>> {
        use std::ops::Bound;

        let (start, end) = match (self.region.start(), self.region.end()) {
            (Bound::Included(s), Bound::Included(e)) => (s, e),
            _ => return Err("Cannot create windows for unbounded regions".into()),
        };

        let mut windows = Vec::new();
        let mut current_start = start;

        while usize::from(current_start) + self.window_size <= usize::from(end) {
            let window_end = Position::try_from(usize::from(current_start) + self.window_size - 1)?;
            let window_region = Region::new(self.region.name(), current_start..=window_end);
            windows.push(window_region);

            current_start = Position::try_from(usize::from(current_start) + self.step_size)?;
        }

        Ok(windows)
    }
}

// Example usage
let region = Region::new("chr1", Position::try_from(1000)?..=Position::try_from(2000)?);
let windower = GenomicWindow::new(region, 100, 50); // 100bp windows, 50bp step

let windows = windower.windows()?;
for (i, window) in windows.iter().enumerate() {
    println!("Window {}: {}", i + 1, window);
}
                </div>

                <h3>Example 3: Coordinate System Conversion</h3>
                <div class="code-block">
// Convert between different coordinate systems
struct CoordinateConverter;

impl CoordinateConverter {
    // Convert GFF3 coordinates to BED format
    fn gff3_to_bed(region: &Region) -> Option<(String, usize, usize)> {
        use std::ops::Bound;

        match (region.start(), region.end()) {
            (Bound::Included(start), Bound::Included(end)) => {
                let bed_start = usize::from(start) - 1; // Convert to 0-based
                let bed_end = usize::from(end);          // Keep as-is for half-open interval
                Some((region.name().to_string(), bed_start, bed_end))
            }
            _ => None, // Can't convert unbounded regions
        }
    }

    // Convert BED coordinates to GFF3 format
    fn bed_to_gff3(chr: &str, start: usize, end: usize) -> Result<Region, Box<dyn std::error::Error>> {
        let gff3_start = Position::try_from(start + 1)?; // Convert to 1-based
        let gff3_end = Position::try_from(end)?;         // Keep as-is
        Ok(Region::new(chr, gff3_start..=gff3_end))
    }

    // Validate coordinate conversion
    fn validate_conversion(original_region: &Region) -> Result<bool, Box<dyn std::error::Error>> {
        if let Some((chr, bed_start, bed_end)) = Self::gff3_to_bed(original_region) {
            let converted_back = Self::bed_to_gff3(&chr, bed_start, bed_end)?;
            Ok(*original_region == converted_back)
        } else {
            Ok(false)
        }
    }
}

// Example usage
let gff3_region = Region::new("chr1", Position::try_from(1000)?..=Position::try_from(2000)?);
println!("GFF3 region: {}", gff3_region);

if let Some((chr, start, end)) = CoordinateConverter::gff3_to_bed(&gff3_region) {
    println!("BED format: {}\t{}\t{}", chr, start, end);

    let back_to_gff3 = CoordinateConverter::bed_to_gff3(&chr, start, end)?;
    println!("Back to GFF3: {}", back_to_gff3);

    let is_valid = CoordinateConverter::validate_conversion(&gff3_region)?;
    println!("Conversion is valid: {}", is_valid);
}
                </div>
            </section>

            <!-- Best Practices Section -->
            <section id="best-practices" class="section">
                <h2>🎯 Best Practices</h2>

                <h3>Type Safety Guidelines</h3>
                <div class="highlight">
                    <h4>✅ Do's</h4>
                    <ul>
                        <li>Always use <code>Position</code> for 1-based genomic coordinates</li>
                        <li>Use <code>try_from()</code> for safe position creation</li>
                        <li>Prefer <code>get()</code> over <code>index()</code> for sequence access</li>
                        <li>Use <code>Region</code> for genomic location specifications</li>
                        <li>Handle parsing errors explicitly</li>
                        <li>Validate coordinate ranges before operations</li>
                    </ul>
                </div>

                <div class="warning">
                    <h4>❌ Don'ts</h4>
                    <ul>
                        <li>Don't use raw <code>usize</code> for genomic positions</li>
                        <li>Don't assume positions are valid without checking</li>
                        <li>Don't mix coordinate systems without explicit conversion</li>
                        <li>Don't ignore overflow in arithmetic operations</li>
                        <li>Don't use <code>unwrap()</code> on position creation</li>
                    </ul>
                </div>

                <h3>Performance Considerations</h3>
                <div class="code-block">
// Efficient position operations
use noodles_core::Position;

// ✅ Good: Batch position creation
fn create_positions_batch(values: &[usize]) -> Vec<Option<Position>> {
    values.iter().map(|&v| Position::new(v)).collect()
}

// ✅ Good: Reuse positions when possible
let base_pos = Position::try_from(1000)?;
let positions: Vec<_> = (0..100)
    .filter_map(|i| base_pos.checked_add(i))
    .collect();

// ❌ Avoid: Repeated parsing
// for i in 0..1000 {
//     let pos: Position = format!("{}", i + 1).parse().unwrap();
// }

// ✅ Better: Direct creation
let positions: Vec<_> = (1..=1000)
    .filter_map(Position::new)
    .collect();
                </div>

                <h3>Error Handling Best Practices</h3>
                <div class="code-block">
// Comprehensive error handling
use noodles_core::{Position, Region};

// ✅ Good: Explicit error handling
fn safe_genomic_operation(chr: &str, start: usize, end: usize) -> Result<Region, Box<dyn std::error::Error>> {
    // Validate inputs
    if start == 0 {
        return Err("Start position cannot be zero (1-based coordinates)".into());
    }
    if start > end {
        return Err(format!("Start position {} > end position {}", start, end).into());
    }

    // Safe position creation
    let start_pos = Position::try_from(start)?;
    let end_pos = Position::try_from(end)?;

    // Create region
    Ok(Region::new(chr, start_pos..=end_pos))
}

// ✅ Good: Graceful degradation
fn parse_regions_tolerant(input: &str) -> (Vec<Region>, Vec<String>) {
    let mut valid_regions = Vec::new();
    let mut errors = Vec::new();

    for line in input.lines() {
        match line.trim().parse::<Region>() {
            Ok(region) => valid_regions.push(region),
            Err(e) => errors.push(format!("Failed to parse '{}': {}", line, e)),
        }
    }

    (valid_regions, errors)
}
                </div>

                <h3>Testing Strategies</h3>
                <div class="code-block">
#[cfg(test)]
mod tests {
    use super::*;
    use noodles_core::{Position, Region};

    #[test]
    fn test_position_creation() {
        // Test valid positions
        assert!(Position::new(1).is_some());
        assert!(Position::new(1000).is_some());
        assert!(Position::new(usize::MAX).is_some());

        // Test invalid positions
        assert!(Position::new(0).is_none());
    }

    #[test]
    fn test_position_arithmetic() {
        let pos = Position::try_from(100).unwrap();

        // Test safe addition
        assert_eq!(pos.checked_add(50), Position::new(150));
        assert_eq!(pos.checked_add(usize::MAX), None);
    }

    #[test]
    fn test_region_parsing() {
        // Test valid regions
        assert!("chr1".parse::<Region>().is_ok());
        assert!("chr1:1000".parse::<Region>().is_ok());
        assert!("chr1:1000-2000".parse::<Region>().is_ok());

        // Test invalid regions
        assert!("".parse::<Region>().is_err());
        assert!("chr1:0".parse::<Region>().is_err());
        assert!("chr1:abc".parse::<Region>().is_err());
    }

    #[test]
    fn test_coordinate_conversion() {
        let pos = Position::try_from(5).unwrap();
        let zero_based = usize::from(pos) - 1;
        assert_eq!(zero_based, 4);

        let back_to_one_based = Position::try_from(zero_based + 1).unwrap();
        assert_eq!(back_to_one_based, pos);
    }
}
                </div>
            </section>

            <!-- Visualization Section -->
            <section id="visualization" class="section">
                <h2>📊 Core Concepts Visualization</h2>

                <h3>Position and Interval Relationships</h3>
                <div style="text-align: center; margin: 2rem 0;">
                    <svg width="900" height="500" viewBox="0 0 900 500" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="900" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                        <!-- Title -->
                        <text x="450" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Noodles Core Concepts Overview</text>

                        <!-- Position Section -->
                        <rect x="50" y="50" width="800" height="80" fill="#e67e22" stroke="#d35400" stroke-width="2" rx="8"/>
                        <text x="450" y="75" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Position: Type-Safe 1-based Coordinates</text>

                        <!-- Position examples -->
                        <circle cx="150" cy="100" r="15" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
                        <text x="150" y="105" text-anchor="middle" font-size="10" fill="white">1</text>
                        <text x="150" y="125" text-anchor="middle" font-size="9" fill="#2c3e50">MIN</text>

                        <circle cx="300" cy="100" r="15" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
                        <text x="300" y="105" text-anchor="middle" font-size="10" fill="white">1000</text>
                        <text x="300" y="125" text-anchor="middle" font-size="9" fill="#2c3e50">Valid</text>

                        <circle cx="450" cy="100" r="15" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
                        <text x="450" y="105" text-anchor="middle" font-size="10" fill="white">MAX</text>
                        <text x="450" y="125" text-anchor="middle" font-size="9" fill="#2c3e50">usize::MAX</text>

                        <circle cx="600" cy="100" r="15" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                        <text x="600" y="105" text-anchor="middle" font-size="10" fill="white">0</text>
                        <text x="600" y="125" text-anchor="middle" font-size="9" fill="#2c3e50">Invalid</text>

                        <text x="750" y="105" font-size="12" fill="white">NonZeroUsize wrapper</text>

                        <!-- Interval Section -->
                        <rect x="50" y="150" width="800" height="100" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="8"/>
                        <text x="450" y="175" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Interval: Mathematical Ranges</text>

                        <!-- Interval types -->
                        <line x1="100" y1="200" x2="200" y2="200" stroke="white" stroke-width="4"/>
                        <circle cx="100" cy="200" r="4" fill="white"/>
                        <circle cx="200" cy="200" r="4" fill="white"/>
                        <text x="150" y="220" text-anchor="middle" font-size="10" fill="white">[a, b]</text>
                        <text x="150" y="235" text-anchor="middle" font-size="9" fill="white">Closed</text>

                        <line x1="250" y1="200" x2="350" y2="200" stroke="white" stroke-width="4"/>
                        <circle cx="250" cy="200" r="4" fill="white"/>
                        <polygon points="350,195 360,200 350,205" fill="white"/>
                        <text x="300" y="220" text-anchor="middle" font-size="10" fill="white">[a, ∞)</text>
                        <text x="300" y="235" text-anchor="middle" font-size="9" fill="white">Right-unbounded</text>

                        <line x1="400" y1="200" x2="500" y2="200" stroke="white" stroke-width="4"/>
                        <polygon points="400,195 390,200 400,205" fill="white"/>
                        <circle cx="500" cy="200" r="4" fill="white"/>
                        <text x="450" y="220" text-anchor="middle" font-size="10" fill="white">(-∞, b]</text>
                        <text x="450" y="235" text-anchor="middle" font-size="9" fill="white">Left-unbounded</text>

                        <line x1="550" y1="200" x2="650" y2="200" stroke="white" stroke-width="4"/>
                        <polygon points="550,195 540,200 550,205" fill="white"/>
                        <polygon points="650,195 660,200 650,205" fill="white"/>
                        <text x="600" y="220" text-anchor="middle" font-size="10" fill="white">(-∞, ∞)</text>
                        <text x="600" y="235" text-anchor="middle" font-size="9" fill="white">Unbounded</text>

                        <!-- Region Section -->
                        <rect x="50" y="270" width="800" height="100" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="8"/>
                        <text x="450" y="295" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Region: Genomic Locations</text>

                        <!-- Region examples -->
                        <rect x="100" y="310" width="150" height="40" fill="#a569bd" stroke="#9b59b6" stroke-width="1" rx="4"/>
                        <text x="175" y="325" text-anchor="middle" font-size="11" fill="white">chr1</text>
                        <text x="175" y="340" text-anchor="middle" font-size="9" fill="white">Entire chromosome</text>

                        <rect x="270" y="310" width="150" height="40" fill="#a569bd" stroke="#9b59b6" stroke-width="1" rx="4"/>
                        <text x="345" y="325" text-anchor="middle" font-size="11" fill="white">chr1:1000</text>
                        <text x="345" y="340" text-anchor="middle" font-size="9" fill="white">From position to end</text>

                        <rect x="440" y="310" width="150" height="40" fill="#a569bd" stroke="#9b59b6" stroke-width="1" rx="4"/>
                        <text x="515" y="325" text-anchor="middle" font-size="11" fill="white">chr1:1000-2000</text>
                        <text x="515" y="340" text-anchor="middle" font-size="9" fill="white">Specific range</text>

                        <rect x="610" y="310" width="150" height="40" fill="#a569bd" stroke="#9b59b6" stroke-width="1" rx="4"/>
                        <text x="685" y="325" text-anchor="middle" font-size="11" fill="white">scaffold_1:500</text>
                        <text x="685" y="340" text-anchor="middle" font-size="9" fill="white">Non-chromosome</text>

                        <!-- Operations Section -->
                        <rect x="50" y="390" width="800" height="80" fill="#27ae60" stroke="#229954" stroke-width="2" rx="8"/>
                        <text x="450" y="415" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Core Operations</text>

                        <!-- Operation examples -->
                        <rect x="80" y="435" width="120" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="140" y="450" text-anchor="middle" font-size="9" fill="white">Position Creation</text>

                        <rect x="220" y="435" width="120" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="280" y="450" text-anchor="middle" font-size="9" fill="white">Safe Indexing</text>

                        <rect x="360" y="435" width="120" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="420" y="450" text-anchor="middle" font-size="9" fill="white">Interval Math</text>

                        <rect x="500" y="435" width="120" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="560" y="450" text-anchor="middle" font-size="9" fill="white">Region Parsing</text>

                        <rect x="640" y="435" width="120" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="700" y="450" text-anchor="middle" font-size="9" fill="white">Coordinate Conversion</text>
                    </svg>
                </div>

                <h3>Conclusion</h3>
                <p>The <code>noodles-core</code> library provides the essential foundation for safe and efficient genomic computing in Rust. By leveraging Rust's type system, it eliminates common coordinate system errors while maintaining zero-cost abstractions.</p>

                <div class="core-feature">
                    <h4>🚀 Key Takeaways</h4>
                    <ul>
                        <li><strong>Type Safety</strong> - Positions cannot be zero, preventing off-by-one errors</li>
                        <li><strong>Mathematical Correctness</strong> - Proper interval arithmetic and overlap detection</li>
                        <li><strong>Zero-cost Abstractions</strong> - No runtime overhead for safety guarantees</li>
                        <li><strong>Interoperability</strong> - Easy integration with all noodles libraries</li>
                        <li><strong>Explicit Conversions</strong> - Clear coordinate system transformations</li>
                    </ul>
                </div>

                <div class="info">
                    <h4>🚀 Next Steps</h4>
                    <p>To get started with noodles-core:</p>
                    <ol>
                        <li>Add <code>noodles-core = "0.18"</code> to your Cargo.toml</li>
                        <li>Start using <code>Position</code> for all genomic coordinates</li>
                        <li>Use <code>Region</code> for genomic location specifications</li>
                        <li>Leverage <code>SequenceIndex</code> for safe sequence access</li>
                        <li>Build upon this foundation with other noodles libraries</li>
                        <li>Contribute to the noodles ecosystem</li>
                    </ol>
                </div>
            </section>
        </div>
    </div>

    <script>
        // Simulate loading progress
        let progress = 0;
        const progressBar = document.getElementById('progress');
        const loadingDiv = document.getElementById('loading');
        const contentDiv = document.getElementById('content');

        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
                setTimeout(() => {
                    loadingDiv.style.display = 'none';
                    contentDiv.style.display = 'block';
                    loadContent();
                }, 500);
            }
            progressBar.style.width = progress + '%';
        }, 200);

        function loadContent() {
            // Add smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add copy functionality to code blocks
            document.querySelectorAll('.code-block').forEach(block => {
                const copyButton = document.createElement('button');
                copyButton.textContent = '📋 Copy';
                copyButton.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    background: #e67e22;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                    z-index: 10;
                `;

                block.style.position = 'relative';
                block.appendChild(copyButton);

                copyButton.addEventListener('click', () => {
                    const text = block.textContent.replace('📋 Copy', '').trim();
                    navigator.clipboard.writeText(text).then(() => {
                        copyButton.textContent = '✅ Copied!';
                        setTimeout(() => {
                            copyButton.textContent = '📋 Copy';
                        }, 2000);
                    });
                });
            });

            // Add section highlighting on scroll
            const sections = document.querySelectorAll('.section');
            const tocLinks = document.querySelectorAll('.toc a');

            function highlightCurrentSection() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (window.scrollY >= sectionTop - 100) {
                        current = section.getAttribute('id');
                    }
                });

                tocLinks.forEach(link => {
                    link.style.color = '';
                    link.style.fontWeight = '';
                    if (link.getAttribute('href') === '#' + current) {
                        link.style.color = '#e74c3c';
                        link.style.fontWeight = 'bold';
                    }
                });
            }

            window.addEventListener('scroll', highlightCurrentSection);
            highlightCurrentSection(); // Initial call

            // Add interactive elements
            console.log('🧬 Noodles Core Tutorial loaded successfully!');
            console.log('📚 Navigate using the table of contents');
            console.log('📋 Click copy buttons on code blocks to copy examples');
            console.log('🔧 This tutorial covers the foundation types of noodles');
        }

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K to focus search (if we had search)
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                // Could implement search functionality here
            }

            // Escape to scroll to top
            if (e.key === 'Escape') {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        });

        // Add print styles
        const printStyles = `
            @media print {
                .toc { page-break-after: always; }
                .section { page-break-inside: avoid; }
                .code-block { page-break-inside: avoid; }
                svg { max-width: 100%; height: auto; }
                .feature-grid { display: block; }
                .feature-card { margin-bottom: 1rem; }
                .math-formula { page-break-inside: avoid; }
            }
        `;

        const styleSheet = document.createElement('style');
        styleSheet.textContent = printStyles;
        document.head.appendChild(styleSheet);
    </script>
</body>
</html>
