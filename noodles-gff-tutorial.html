<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Noodles GFF Tutorial - Complete Guide to GFF3 Handling in Rust</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --info-color: #8e44ad;
            --background-color: #ecf0f1;
            --text-color: #2c3e50;
            --code-bg: #f8f9fa;
            --border-color: #bdc3c7;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem 0;
            text-align: center;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .toc {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            border-left: 4px solid var(--secondary-color);
        }

        .toc h2 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .toc ul {
            list-style: none;
        }

        .toc li {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }

        .toc a {
            color: var(--secondary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .toc a:hover {
            color: var(--accent-color);
        }

        .section {
            background: white;
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid var(--secondary-color);
        }

        h2 {
            color: var(--primary-color);
            font-size: 2rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
        }

        h3 {
            color: var(--secondary-color);
            font-size: 1.5rem;
            margin: 1.5rem 0 1rem 0;
        }

        h4 {
            color: var(--primary-color);
            font-size: 1.2rem;
            margin: 1rem 0 0.5rem 0;
        }

        .code-block {
            background: var(--code-bg);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
            word-break: break-all;
            overflow-wrap: break-word;
        }

        .highlight {
            background: linear-gradient(120deg, #a8e6cf 0%, #dcedc1 100%);
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid var(--success-color);
            margin: 1rem 0;
        }

        .warning {
            background: linear-gradient(120deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid var(--warning-color);
            margin: 1rem 0;
        }

        .info {
            background: linear-gradient(120deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }

        .api-method {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .method-signature {
            font-family: 'Courier New', monospace;
            background: var(--primary-color);
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: var(--secondary-color);
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
            width: 0%;
            transition: width 0.3s ease;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .feature-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .feature-card h4 {
            color: var(--info-color);
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🧬 Noodles GFF Tutorial</h1>
            <p class="subtitle">Complete Guide to GFF3 Handling in Rust with the Noodles Library</p>
        </header>

        <div class="loading" id="loading">
            <h3>Loading Tutorial Content...</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progress"></div>
            </div>
        </div>

        <div id="content" style="display: none;">
            <!-- Table of Contents -->
            <div class="toc">
                <h2>📚 Table of Contents</h2>
                <ul>
                    <li><a href="#introduction">1. Introduction to GFF3 and Noodles</a></li>
                    <li><a href="#architecture">2. Library Architecture</a></li>
                    <li><a href="#core-types">3. Core Types and Structures</a></li>
                    <li><a href="#directives">4. GFF3 Directives and Metadata</a></li>
                    <li><a href="#reading">5. Reading GFF3 Files</a></li>
                    <li><a href="#writing">6. Writing GFF3 Files</a></li>
                    <li><a href="#features">7. Working with Features</a></li>
                    <li><a href="#attributes">8. Attributes and Annotations</a></li>
                    <li><a href="#querying">9. Querying and Indexing</a></li>
                    <li><a href="#async">10. Async Operations</a></li>
                    <li><a href="#examples">11. Practical Examples</a></li>
                    <li><a href="#best-practices">12. Best Practices</a></li>
                    <li><a href="#visualization">13. Data Flow Visualization</a></li>
                </ul>
            </div>

            <!-- Introduction Section -->
            <section id="introduction" class="section">
                <h2>🧬 Introduction to GFF3 and Noodles</h2>

                <h3>What is GFF3?</h3>
                <p>The <strong>Generic Feature Format version 3 (GFF3)</strong> is a standardized tab-delimited text format for representing genomic features and annotations. It's widely used in bioinformatics for storing information about genes, transcripts, exons, regulatory elements, and other genomic features.</p>

                <div class="highlight">
                    <h4>GFF3 Format Structure</h4>
                    <p>Each GFF3 record contains 9 tab-separated fields:</p>
                    <ol>
                        <li><strong>seqid</strong> - Reference sequence identifier</li>
                        <li><strong>source</strong> - Source of the annotation</li>
                        <li><strong>type</strong> - Feature type (gene, mRNA, exon, etc.)</li>
                        <li><strong>start</strong> - Start position (1-based)</li>
                        <li><strong>end</strong> - End position (1-based, inclusive)</li>
                        <li><strong>score</strong> - Confidence score (or '.')</li>
                        <li><strong>strand</strong> - Strand ('+', '-', or '.')</li>
                        <li><strong>phase</strong> - Reading frame (0, 1, 2, or '.')</li>
                        <li><strong>attributes</strong> - Semicolon-separated key=value pairs</li>
                    </ol>
                </div>

                <h3>GFF3 vs GTF</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>GFF3 Features</h4>
                        <ul>
                            <li>Hierarchical relationships via Parent/ID</li>
                            <li>Flexible attribute system</li>
                            <li>URL encoding for special characters</li>
                            <li>Directives for metadata</li>
                            <li>FASTA sequences can be embedded</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>GTF Features</h4>
                        <ul>
                            <li>Gene-centric structure</li>
                            <li>Required gene_id and transcript_id</li>
                            <li>Simpler attribute format</li>
                            <li>More restrictive specification</li>
                            <li>Widely used for RNA-seq analysis</li>
                        </ul>
                    </div>
                </div>

                <h3>Why Noodles GFF?</h3>
                <p>The <code>noodles-gff</code> crate provides a comprehensive, efficient, and type-safe way to handle GFF3 files in Rust. It offers:</p>
                <ul>
                    <li>🚀 <strong>High Performance</strong> - Zero-copy parsing and efficient memory usage</li>
                    <li>🔒 <strong>Memory Safety</strong> - Rust's ownership system prevents common errors</li>
                    <li>🎯 <strong>Type Safety</strong> - Strong typing for genomic coordinates and features</li>
                    <li>📚 <strong>Rich API</strong> - Comprehensive methods for reading, writing, and querying</li>
                    <li>🔍 <strong>Indexing Support</strong> - Fast random access with CSI indices</li>
                    <li>⚡ <strong>Async Support</strong> - Non-blocking I/O operations</li>
                    <li>🏗️ <strong>Directive Handling</strong> - Full support for GFF3 metadata directives</li>
                </ul>

                <div class="code-block">
// Example GFF3 record
##gff-version 3
chr1    HAVANA  gene    11869   14409   .   +   .   ID=gene:ENSG00000223972;Name=DDX11L1;biotype=transcribed_unprocessed_pseudogene
chr1    HAVANA  transcript  11869   14409   .   +   .   ID=transcript:ENST00000456328;Parent=gene:ENSG00000223972;Name=DDX11L1-202
chr1    HAVANA  exon    11869   12227   .   +   .   ID=exon:ENSE00002234944;Parent=transcript:ENST00000456328
                </div>
            </section>

            <!-- Architecture Section -->
            <section id="architecture" class="section">
                <h2>🏗️ Library Architecture</h2>

                <h3>Module Structure</h3>
                <p>The <code>noodles-gff</code> library is organized into several key modules:</p>

                <div class="api-method">
                    <h4>Core Modules</h4>
                    <ul>
                        <li><code>io</code> - Input/output operations (Reader, Writer)</li>
                        <li><code>feature</code> - Feature record representation and parsing</li>
                        <li><code>directive</code> - GFF3 directive handling</li>
                        <li><code>directive_buf</code> - Owned directive structures</li>
                        <li><code>line</code> - Line-level parsing and classification</li>
                        <li><code>line_buf</code> - Buffered line operations</li>
                        <li><code>record</code> - Legacy record interface</li>
                        <li><code>async</code> - Asynchronous I/O operations</li>
                    </ul>
                </div>

                <h3>Dependency Graph</h3>
                <p>The library builds upon several foundational crates:</p>
                <ul>
                    <li><code>noodles-core</code> - Core genomic types (Position, Region)</li>
                    <li><code>noodles-bgzf</code> - Block GZIP compression support</li>
                    <li><code>noodles-csi</code> - Coordinate-sorted indexing</li>
                    <li><code>bstr</code> - Byte string handling</li>
                    <li><code>indexmap</code> - Ordered hash maps for attributes</li>
                    <li><code>percent-encoding</code> - URL encoding/decoding</li>
                    <li><code>tokio</code> - Async runtime (optional)</li>
                </ul>

                <div class="info">
                    <h4>💡 Design Philosophy</h4>
                    <p>The library follows Rust's zero-cost abstractions principle, providing high-level APIs without sacrificing performance. It leverages the type system to prevent common genomics programming errors at compile time and supports both synchronous and asynchronous operations.</p>
                </div>

                <!-- SVG Architecture Diagram -->
                <div style="text-align: center; margin: 2rem 0;">
                    <svg width="900" height="500" viewBox="0 0 900 500" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="900" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                        <!-- Title -->
                        <text x="450" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Noodles GFF Architecture</text>

                        <!-- Application Layer -->
                        <rect x="50" y="60" width="800" height="60" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="5"/>
                        <text x="450" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Application Layer</text>
                        <text x="450" y="105" text-anchor="middle" font-size="12" fill="white">Your Rust Application (Sync & Async)</text>

                        <!-- GFF API Layer -->
                        <rect x="50" y="140" width="800" height="100" fill="#27ae60" stroke="#229954" stroke-width="2" rx="5"/>
                        <text x="450" y="165" text-anchor="middle" font-size="14" font-weight="bold" fill="white">noodles-gff API</text>

                        <!-- API Components -->
                        <rect x="70" y="180" width="100" height="35" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="120" y="200" text-anchor="middle" font-size="9" fill="white">Reader/Writer</text>

                        <rect x="180" y="180" width="100" height="35" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="230" y="200" text-anchor="middle" font-size="9" fill="white">Feature</text>

                        <rect x="290" y="180" width="100" height="35" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="340" y="200" text-anchor="middle" font-size="9" fill="white">Directive</text>

                        <rect x="400" y="180" width="100" height="35" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="450" y="200" text-anchor="middle" font-size="9" fill="white">Attributes</text>

                        <rect x="510" y="180" width="100" height="35" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="560" y="200" text-anchor="middle" font-size="9" fill="white">Line</text>

                        <rect x="620" y="180" width="100" height="35" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="670" y="200" text-anchor="middle" font-size="9" fill="white">Query</text>

                        <rect x="730" y="180" width="100" height="35" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="780" y="200" text-anchor="middle" font-size="9" fill="white">Async</text>

                        <!-- Foundation Layer -->
                        <rect x="50" y="260" width="800" height="80" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="5"/>
                        <text x="450" y="285" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Foundation Libraries</text>

                        <!-- Foundation Components -->
                        <rect x="70" y="300" width="110" height="30" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                        <text x="125" y="318" text-anchor="middle" font-size="9" fill="white">noodles-core</text>

                        <rect x="190" y="300" width="110" height="30" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                        <text x="245" y="318" text-anchor="middle" font-size="9" fill="white">noodles-bgzf</text>

                        <rect x="310" y="300" width="110" height="30" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                        <text x="365" y="318" text-anchor="middle" font-size="9" fill="white">noodles-csi</text>

                        <rect x="430" y="300" width="80" height="30" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                        <text x="470" y="318" text-anchor="middle" font-size="9" fill="white">bstr</text>

                        <rect x="520" y="300" width="80" height="30" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                        <text x="560" y="318" text-anchor="middle" font-size="9" fill="white">indexmap</text>

                        <rect x="610" y="300" width="100" height="30" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                        <text x="660" y="318" text-anchor="middle" font-size="9" fill="white">percent-encoding</text>

                        <rect x="720" y="300" width="80" height="30" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                        <text x="760" y="318" text-anchor="middle" font-size="9" fill="white">tokio</text>

                        <!-- Data Layer -->
                        <rect x="50" y="360" width="800" height="40" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="5"/>
                        <text x="450" y="385" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Data Layer (GFF3 Files, Indices, FASTA)</text>

                        <!-- Arrows -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                            </marker>
                        </defs>

                        <!-- Vertical arrows -->
                        <line x1="450" y1="120" x2="450" y2="135" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="450" y1="240" x2="450" y2="255" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="450" y1="340" x2="450" y2="355" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>

                        <!-- Feature highlights -->
                        <rect x="50" y="420" width="800" height="60" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="5"/>
                        <text x="450" y="440" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Key Features</text>
                        <text x="150" y="460" text-anchor="middle" font-size="10" fill="white">Zero-copy parsing</text>
                        <text x="300" y="460" text-anchor="middle" font-size="10" fill="white">Directive support</text>
                        <text x="450" y="460" text-anchor="middle" font-size="10" fill="white">Hierarchical features</text>
                        <text x="600" y="460" text-anchor="middle" font-size="10" fill="white">Async operations</text>
                        <text x="750" y="460" text-anchor="middle" font-size="10" fill="white">URL encoding</text>
                    </svg>
                </div>
            </section>

            <!-- Core Types Section -->
            <section id="core-types" class="section">
                <h2>🔧 Core Types and Structures</h2>

                <h3>Feature Record Structure</h3>
                <p>The <code>RecordBuf</code> type is the primary structure for representing GFF3 feature records:</p>

                <div class="api-method">
                    <div class="method-signature">pub struct RecordBuf</div>
                    <p>An owned GFF3 feature record with the following fields:</p>
                    <ul>
                        <li><code>reference_sequence_name() -&gt; &amp;BStr</code> - Get sequence identifier</li>
                        <li><code>source() -&gt; &amp;BStr</code> - Get annotation source</li>
                        <li><code>ty() -&gt; &amp;BStr</code> - Get feature type (gene, mRNA, exon)</li>
                        <li><code>start() -&gt; Position</code> - Get start position (1-based)</li>
                        <li><code>end() -&gt; Position</code> - Get end position (1-based, inclusive)</li>
                        <li><code>score() -&gt; Option&lt;f32&gt;</code> - Get confidence score</li>
                        <li><code>strand() -&gt; Strand</code> - Get strand information</li>
                        <li><code>phase() -&gt; Option&lt;Phase&gt;</code> - Get reading frame</li>
                        <li><code>attributes() -&gt; &amp;Attributes</code> - Get feature attributes</li>
                    </ul>
                </div>

                <h3>Line Types and Classification</h3>
                <p>GFF3 files contain different types of lines, represented by the <code>Line</code> enum:</p>

                <div class="code-block">
use noodles_gff::{Line, line::Kind};

// Line classification
match line.kind() {
    Kind::Directive => {
        // Handle directive lines (starting with ##)
        if let Some(directive) = line.as_directive() {
            println!("Directive: {} = {:?}", directive.key(), directive.value());
        }
    }
    Kind::Comment => {
        // Handle comment lines (starting with #)
        if let Some(comment) = line.as_comment() {
            println!("Comment: {}", comment);
        }
    }
    Kind::Record => {
        // Handle GFF3 feature records
        if let Some(result) = line.as_record() {
            let record = result?;
            println!("Feature: {}", record.ty());
        }
    }
}
                </div>

                <h3>Strand and Phase Enums</h3>
                <p>GFF3 uses specific enums for strand and phase information:</p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>Strand</h4>
                        <ul>
                            <li><code>Forward</code> - '+' strand</li>
                            <li><code>Reverse</code> - '-' strand</li>
                            <li><code>Unknown</code> - '.' (unstranded)</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>Phase</h4>
                        <ul>
                            <li><code>Zero</code> - '0' (in-frame)</li>
                            <li><code>One</code> - '1' (frame +1)</li>
                            <li><code>Two</code> - '2' (frame +2)</li>
                        </ul>
                    </div>
                </div>

                <h3>Attributes System</h3>
                <p>GFF3 attributes are key=value pairs that provide additional information about features:</p>

                <div class="highlight">
                    <h4>Attribute Format</h4>
                    <p>The library automatically parses attribute strings like:</p>
                    <code>ID=gene:ENSG00000223972;Name=DDX11L1;biotype=transcribed_unprocessed_pseudogene</code>
                </div>

                <div class="api-method">
                    <div class="method-signature">pub struct Attributes(IndexMap&lt;Tag, Value&gt;)</div>
                    <p>Provides efficient access to attribute data:</p>
                    <ul>
                        <li><code>is_empty() -&gt; bool</code> - Check if attributes exist</li>
                        <li><code>len() -&gt; usize</code> - Get number of attributes</li>
                        <li><code>get(tag: &amp;[u8]) -&gt; Option&lt;&amp;Value&gt;</code> - Get attribute value</li>
                        <li><code>iter() -&gt; Iterator</code> - Iterate over all key-value pairs</li>
                    </ul>
                </div>

                <div class="warning">
                    <h4>⚠️ URL Encoding</h4>
                    <p>GFF3 uses URL encoding for special characters in attribute values. The library automatically handles encoding/decoding of characters like spaces, semicolons, and equals signs.</p>
                </div>
            </section>

            <!-- Directives Section -->
            <section id="directives" class="section">
                <h2>📋 GFF3 Directives and Metadata</h2>

                <h3>Understanding Directives</h3>
                <p>GFF3 directives (also called pragmas) provide metadata about the file and its contents. They start with <code>##</code> and appear at the beginning of files.</p>

                <div class="api-method">
                    <div class="method-signature">pub struct DirectiveBuf</div>
                    <p>Represents a GFF3 directive with key-value structure:</p>
                    <ul>
                        <li><code>new(key, value) -&gt; Self</code> - Create a new directive</li>
                        <li><code>key() -&gt; &amp;BStr</code> - Get directive key</li>
                        <li><code>value() -&gt; Option&lt;&amp;Value&gt;</code> - Get directive value</li>
                    </ul>
                </div>

                <h3>Common GFF3 Directives</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>##gff-version</h4>
                        <p>Specifies the GFF version (always 3 for GFF3)</p>
                        <div class="code-block">##gff-version 3</div>
                    </div>
                    <div class="feature-card">
                        <h4>##sequence-region</h4>
                        <p>Defines sequence boundaries</p>
                        <div class="code-block">##sequence-region chr1 1 248956422</div>
                    </div>
                    <div class="feature-card">
                        <h4>##genome-build</h4>
                        <p>Specifies genome build information</p>
                        <div class="code-block">##genome-build GRCh38 p13</div>
                    </div>
                    <div class="feature-card">
                        <h4>##FASTA</h4>
                        <p>Indicates start of embedded FASTA sequences</p>
                        <div class="code-block">##FASTA</div>
                    </div>
                </div>

                <h3>Working with Directives</h3>
                <div class="code-block">
use noodles_gff::{DirectiveBuf, directive_buf::{key, Value}};

// Create a GFF version directive
let version = DirectiveBuf::new(
    key::GFF_VERSION,
    Some(Value::GffVersion(Default::default())),
);

// Create a sequence region directive
let seq_region = DirectiveBuf::new(
    key::SEQUENCE_REGION,
    Some(Value::SequenceRegion(SequenceRegion::new(
        "chr1",
        Position::try_from(1)?,
        Position::try_from(248956422)?,
    ))),
);

// Write directives
writer.write_directive(&version)?;
writer.write_directive(&seq_region)?;
                </div>

                <h3>Directive Values</h3>
                <p>Different directive types have specific value structures:</p>

                <div class="api-method">
                    <div class="method-signature">pub enum Value</div>
                    <ul>
                        <li><code>GffVersion(GffVersion)</code> - Version information</li>
                        <li><code>SequenceRegion(SequenceRegion)</code> - Sequence boundaries</li>
                        <li><code>GenomeBuild(GenomeBuild)</code> - Genome build info</li>
                        <li><code>String(BString)</code> - Generic string value</li>
                    </ul>
                </div>

                <div class="info">
                    <h4>💡 Directive Processing</h4>
                    <p>Directives provide important context for interpreting GFF3 records. The sequence-region directive, for example, defines the valid coordinate space for features on each sequence.</p>
                </div>
            </section>

            <!-- Reading Section -->
            <section id="reading" class="section">
                <h2>📖 Reading GFF3 Files</h2>

                <h3>Basic File Reading</h3>
                <p>The <code>Reader</code> provides multiple ways to read GFF3 data:</p>

                <div class="code-block">
use std::{fs::File, io::BufReader};
use noodles_gff as gff;

// Open a GFF3 file
let mut reader = File::open("annotations.gff3")
    .map(BufReader::new)
    .map(gff::io::Reader::new)?;

// Method 1: Line-by-line reading
let mut line = gff::Line::default();
while reader.read_line(&mut line)? != 0 {
    match line.kind() {
        gff::line::Kind::Directive => {
            let directive = line.as_directive().unwrap();
            println!("Directive: {}", directive.key());
        }
        gff::line::Kind::Comment => {
            println!("Comment: {}", line.as_comment().unwrap());
        }
        gff::line::Kind::Record => {
            let record = line.as_record().unwrap()?;
            println!("Feature: {} at {}:{}-{}",
                record.ty(),
                record.reference_sequence_name(),
                usize::from(record.start()?),
                usize::from(record.end()?)
            );
        }
    }
}
                </div>

                <h3>Iterator-based Reading</h3>
                <p>For more convenient processing, use the iterator interface:</p>

                <div class="code-block">
// Method 2: Using record iterator
for result in reader.record_bufs() {
    let record = result?;

    // Access record fields
    println!("Feature: {} on sequence {}",
        record.ty(),
        record.reference_sequence_name()
    );

    // Work with attributes
    let attributes = record.attributes();
    if let Some(id) = attributes.get(b"ID") {
        println!("ID: {:?}", id);
    }
    if let Some(parent) = attributes.get(b"Parent") {
        println!("Parent: {:?}", parent);
    }
}
                </div>

                <h3>Line Buffer Iterator</h3>
                <p>Process all line types including directives and comments:</p>

                <div class="code-block">
// Method 3: Using line buffer iterator
for result in reader.line_bufs() {
    let line_buf = result?;

    match line_buf {
        gff::LineBuf::Directive(directive) => {
            println!("Directive: {} = {:?}", directive.key(), directive.value());
        }
        gff::LineBuf::Comment(comment) => {
            println!("Comment: {}", comment);
        }
        gff::LineBuf::Record(record) => {
            println!("Record: {}", record.ty());
        }
    }
}
                </div>

                <h3>Compressed File Support</h3>
                <p>The library seamlessly handles bgzip-compressed files:</p>

                <div class="code-block">
use noodles_bgzf as bgzf;

// Reading compressed GFF3 files
let mut reader = File::open("annotations.gff3.gz")
    .map(bgzf::io::Reader::new)
    .map(gff::io::Reader::new)?;

// Same API as uncompressed files
for result in reader.record_bufs() {
    let record = result?;
    // Process record...
}
                </div>

                <div class="info">
                    <h4>💡 Performance Tip</h4>
                    <p>Use <code>BufReader</code> for better I/O performance when reading large files. The library is designed to minimize memory allocations through zero-copy parsing where possible.</p>
                </div>
            </section>

            <!-- Writing Section -->
            <section id="writing" class="section">
                <h2>✍️ Writing GFF3 Files</h2>

                <h3>Basic Writing</h3>
                <p>The <code>Writer</code> provides methods to create GFF3 files:</p>

                <div class="code-block">
use std::io;
use noodles_gff::{self as gff, DirectiveBuf, LineBuf, feature::RecordBuf};
use noodles_gff::directive_buf::{key, Value};

// Create a writer
let stdout = io::stdout().lock();
let mut writer = gff::io::Writer::new(stdout);

// Write GFF version directive
let version = DirectiveBuf::new(
    key::GFF_VERSION,
    Some(Value::GffVersion(Default::default())),
);
writer.write_directive(&version)?;

// Write a comment
let comment = LineBuf::Comment("Generated by noodles-gff".into());
writer.write_line(&comment)?;

// Write a feature record
let record = RecordBuf::default();
writer.write_record(&record)?;
                </div>

                <h3>Building Feature Records</h3>
                <p>Create custom GFF3 records using the builder pattern:</p>

                <div class="code-block">
use noodles_gff::feature::{RecordBuf, record::{Strand, Phase}};
use noodles_gff::feature::record_buf::{Attributes, attributes::field::{Tag, Value}};
use noodles_core::Position;

// Build attributes
let mut attributes = Attributes::default();
attributes.insert(Tag::from("ID"), Value::from("gene:ENSG00000223972"));
attributes.insert(Tag::from("Name"), Value::from("DDX11L1"));
attributes.insert(Tag::from("biotype"), Value::from("transcribed_unprocessed_pseudogene"));

// Build a gene record
let gene_record = RecordBuf::builder()
    .set_reference_sequence_name("chr1")
    .set_source("HAVANA")
    .set_type("gene")
    .set_start(Position::try_from(11869)?)
    .set_end(Position::try_from(14409)?)
    .set_strand(Strand::Forward)
    .set_attributes(attributes)
    .build();

writer.write_record(&gene_record)?;
                </div>

                <h3>Hierarchical Features</h3>
                <p>Create parent-child relationships using ID and Parent attributes:</p>

                <div class="code-block">
// Create a transcript that belongs to the gene
let mut transcript_attrs = Attributes::default();
transcript_attrs.insert(Tag::from("ID"), Value::from("transcript:ENST00000456328"));
transcript_attrs.insert(Tag::from("Parent"), Value::from("gene:ENSG00000223972"));
transcript_attrs.insert(Tag::from("Name"), Value::from("DDX11L1-202"));

let transcript_record = RecordBuf::builder()
    .set_reference_sequence_name("chr1")
    .set_source("HAVANA")
    .set_type("transcript")
    .set_start(Position::try_from(11869)?)
    .set_end(Position::try_from(14409)?)
    .set_strand(Strand::Forward)
    .set_attributes(transcript_attrs)
    .build();

writer.write_record(&transcript_record)?;

// Create an exon that belongs to the transcript
let mut exon_attrs = Attributes::default();
exon_attrs.insert(Tag::from("ID"), Value::from("exon:ENSE00002234944"));
exon_attrs.insert(Tag::from("Parent"), Value::from("transcript:ENST00000456328"));

let exon_record = RecordBuf::builder()
    .set_reference_sequence_name("chr1")
    .set_source("HAVANA")
    .set_type("exon")
    .set_start(Position::try_from(11869)?)
    .set_end(Position::try_from(12227)?)
    .set_strand(Strand::Forward)
    .set_attributes(exon_attrs)
    .build();

writer.write_record(&exon_record)?;
                </div>
            </section>

            <!-- Features Section -->
            <section id="features" class="section">
                <h2>🧬 Working with Features</h2>

                <h3>Feature Hierarchies</h3>
                <p>GFF3 supports hierarchical relationships between features using ID and Parent attributes:</p>

                <div style="text-align: center; margin: 2rem 0;">
                    <svg width="700" height="300" viewBox="0 0 700 300" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="700" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                        <!-- Title -->
                        <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">GFF3 Feature Hierarchy</text>

                        <!-- Gene level -->
                        <rect x="50" y="50" width="600" height="40" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="5"/>
                        <text x="350" y="75" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Gene (ID=gene:ENSG00000223972)</text>

                        <!-- Transcript level -->
                        <rect x="100" y="110" width="500" height="35" fill="#27ae60" stroke="#229954" stroke-width="2" rx="5"/>
                        <text x="350" y="132" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Transcript (ID=transcript:ENST00000456328, Parent=gene:ENSG00000223972)</text>

                        <!-- Exon level -->
                        <rect x="120" y="165" width="120" height="30" fill="#e74c3c" stroke="#c0392b" stroke-width="1" rx="3"/>
                        <text x="180" y="183" text-anchor="middle" font-size="10" fill="white">Exon 1</text>

                        <rect x="260" y="165" width="120" height="30" fill="#e74c3c" stroke="#c0392b" stroke-width="1" rx="3"/>
                        <text x="320" y="183" text-anchor="middle" font-size="10" fill="white">Exon 2</text>

                        <rect x="400" y="165" width="120" height="30" fill="#e74c3c" stroke="#c0392b" stroke-width="1" rx="3"/>
                        <text x="460" y="183" text-anchor="middle" font-size="10" fill="white">Exon 3</text>

                        <!-- CDS level -->
                        <rect x="140" y="215" width="80" height="25" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
                        <text x="180" y="230" text-anchor="middle" font-size="9" fill="white">CDS 1</text>

                        <rect x="280" y="215" width="80" height="25" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
                        <text x="320" y="230" text-anchor="middle" font-size="9" fill="white">CDS 2</text>

                        <rect x="420" y="215" width="80" height="25" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
                        <text x="460" y="230" text-anchor="middle" font-size="9" fill="white">CDS 3</text>

                        <!-- Arrows -->
                        <defs>
                            <marker id="arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#2c3e50"/>
                            </marker>
                        </defs>

                        <!-- Hierarchy arrows -->
                        <line x1="350" y1="90" x2="350" y2="105" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
                        <line x1="180" y1="145" x2="180" y2="160" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrow)"/>
                        <line x1="320" y1="145" x2="320" y2="160" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrow)"/>
                        <line x1="460" y1="145" x2="460" y2="160" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrow)"/>
                        <line x1="180" y1="195" x2="180" y2="210" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrow)"/>
                        <line x1="320" y1="195" x2="320" y2="210" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrow)"/>
                        <line x1="460" y1="195" x2="460" y2="210" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrow)"/>

                        <!-- Legend -->
                        <text x="50" y="270" font-size="12" font-weight="bold" fill="#2c3e50">Hierarchy: Gene → Transcript → Exon → CDS</text>
                        <text x="50" y="285" font-size="10" fill="#7f8c8d">Each child feature references its parent via the Parent attribute</text>
                    </svg>
                </div>

                <h3>Common Feature Types</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>Sequence Features</h4>
                        <ul>
                            <li><code>gene</code> - Gene locus</li>
                            <li><code>pseudogene</code> - Non-functional gene</li>
                            <li><code>transposable_element</code> - Mobile element</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>Transcript Features</h4>
                        <ul>
                            <li><code>mRNA</code> - Protein-coding transcript</li>
                            <li><code>lnc_RNA</code> - Long non-coding RNA</li>
                            <li><code>miRNA</code> - MicroRNA</li>
                            <li><code>tRNA</code> - Transfer RNA</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>Structural Features</h4>
                        <ul>
                            <li><code>exon</code> - Exonic region</li>
                            <li><code>CDS</code> - Coding sequence</li>
                            <li><code>UTR</code> - Untranslated region</li>
                            <li><code>intron</code> - Intronic region</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>Regulatory Features</h4>
                        <ul>
                            <li><code>promoter</code> - Promoter region</li>
                            <li><code>enhancer</code> - Enhancer element</li>
                            <li><code>silencer</code> - Silencer element</li>
                            <li><code>TFBS</code> - Transcription factor binding site</li>
                        </ul>
                    </div>
                </div>

                <h3>Feature Validation</h3>
                <p>The library provides methods to validate feature relationships and coordinates:</p>

                <div class="code-block">
// Validate coordinate ranges
fn validate_feature_coordinates(record: &RecordBuf) -> Result<(), String> {
    let start = record.start();
    let end = record.end();

    if start > end {
        return Err(format!("Invalid coordinates: start {} > end {}",
            usize::from(start), usize::from(end)));
    }

    // Additional validation logic...
    Ok(())
}

// Check for required attributes
fn validate_feature_attributes(record: &RecordBuf) -> Result<(), String> {
    let attributes = record.attributes();

    // ID is required for most features
    if attributes.get(b"ID").is_none() {
        return Err("Missing required ID attribute".to_string());
    }

    // Parent is required for child features
    if matches!(record.ty().as_ref(), b"exon" | b"CDS" | b"UTR") {
        if attributes.get(b"Parent").is_none() {
            return Err("Missing required Parent attribute for child feature".to_string());
        }
    }

    Ok(())
}
                </div>
            </section>

            <!-- Attributes Section -->
            <section id="attributes" class="section">
                <h2>🏷️ Attributes and Annotations</h2>

                <h3>Attribute Access Patterns</h3>
                <p>GFF3 attributes contain essential metadata about genomic features:</p>

                <div class="code-block">
// Common GFF3 attributes
let attributes = record.attributes();

// Essential attributes
if let Some(id) = attributes.get(b"ID") {
    println!("Feature ID: {:?}", id);
}

if let Some(parent) = attributes.get(b"Parent") {
    println!("Parent feature: {:?}", parent);
}

if let Some(name) = attributes.get(b"Name") {
    println!("Feature name: {:?}", name);
}

// Biotype information
if let Some(biotype) = attributes.get(b"biotype") {
    println!("Biotype: {:?}", biotype);
}

// Iterate over all attributes
for (tag, value) in attributes.iter() {
    println!("{}: {:?}", tag, value);
}
                </div>

                <h3>URL Encoding/Decoding</h3>
                <p>GFF3 uses URL encoding for special characters in attribute values:</p>

                <div class="highlight">
                    <h4>Encoded Characters</h4>
                    <ul>
                        <li><code>%3B</code> - Semicolon (;)</li>
                        <li><code>%3D</code> - Equals sign (=)</li>
                        <li><code>%26</code> - Ampersand (&)</li>
                        <li><code>%2C</code> - Comma (,)</li>
                        <li><code>%20</code> - Space</li>
                        <li><code>%09</code> - Tab</li>
                        <li><code>%0A</code> - Newline</li>
                        <li><code>%0D</code> - Carriage return</li>
                        <li><code>%25</code> - Percent sign (%)</li>
                    </ul>
                </div>

                <div class="code-block">
use percent_encoding::{percent_decode_str, utf8_percent_encode, NON_ALPHANUMERIC};

// The library automatically handles encoding/decoding
let encoded_value = "Gene%20with%20spaces";
let decoded_value = percent_decode_str(encoded_value).decode_utf8()?;
println!("Decoded: {}", decoded_value); // "Gene with spaces"

// When creating attributes, encoding is handled automatically
let mut attributes = Attributes::default();
attributes.insert(Tag::from("Note"), Value::from("Gene with spaces; special chars"));
// Automatically encoded as: Note=Gene%20with%20spaces%3B%20special%20chars
                </div>

                <h3>Multi-value Attributes</h3>
                <p>Some attributes can have multiple values separated by commas:</p>

                <div class="code-block">
// Handle multi-value attributes
if let Some(dbxref) = attributes.get(b"Dbxref") {
    match dbxref {
        Value::String(s) => {
            // Split on commas for multiple values
            let refs: Vec&lt;&str&gt; = s.split(',').collect();
            for db_ref in refs {
                println!("Database reference: {}", db_ref);
            }
        }
        Value::Array(values) => {
            // Some implementations use arrays
            for value in values {
                println!("Database reference: {:?}", value);
            }
        }
    }
}
                </div>
            </section>

            <!-- Querying Section -->
            <section id="querying" class="section">
                <h2>🔍 Querying and Indexing</h2>

                <h3>Indexed Queries</h3>
                <p>For large GFF3 files, use coordinate-sorted indices (CSI) for fast random access:</p>

                <div class="code-block">
use noodles_bgzf as bgzf;
use noodles_csi as csi;
use noodles_core::Region;

// Load the index
let index = csi::fs::read("annotations.gff3.gz.csi")?;

// Open the compressed GFF3 file
let mut reader = File::open("annotations.gff3.gz")
    .map(bgzf::io::Reader::new)
    .map(gff::io::Reader::new)?;

// Define a genomic region
let region: Region = "chr1:10000-20000".parse()?;

// Query the region
let query = reader.query(&index, &region)?;

for result in query {
    let record = result?;
    println!("Found feature: {} at {}:{}-{}",
        record.ty(),
        record.reference_sequence_name(),
        usize::from(record.start()),
        usize::from(record.end())
    );
}
                </div>

                <h3>Feature Filtering</h3>
                <p>Filter features by type, attributes, or other criteria:</p>

                <div class="code-block">
// Filter by feature type
fn filter_genes(reader: &mut gff::io::Reader&lt;impl BufRead&gt;) -> Result&lt;Vec&lt;RecordBuf&gt;, Box&lt;dyn std::error::Error&gt;&gt; {
    let mut genes = Vec::new();

    for result in reader.record_bufs() {
        let record = result?;

        if record.ty() == "gene" {
            genes.push(record);
        }
    }

    Ok(genes)
}

// Filter by attribute value
fn filter_by_biotype(
    reader: &mut gff::io::Reader&lt;impl BufRead&gt;,
    target_biotype: &str
) -> Result&lt;Vec&lt;RecordBuf&gt;, Box&lt;dyn std::error::Error&gt;&gt; {
    let mut filtered = Vec::new();

    for result in reader.record_bufs() {
        let record = result?;

        if let Some(biotype) = record.attributes().get(b"biotype") {
            if biotype.as_ref() == target_biotype.as_bytes() {
                filtered.push(record);
            }
        }
    }

    Ok(filtered)
}
                </div>

                <div class="warning">
                    <h4>⚠️ Index Requirements</h4>
                    <p>Indexed queries require:</p>
                    <ul>
                        <li>GFF3 file compressed with bgzip</li>
                        <li>Coordinate-sorted records</li>
                        <li>CSI index file (.csi extension)</li>
                        <li>Consistent sequence naming between file and queries</li>
                    </ul>
                </div>
            </section>

            <!-- Async Section -->
            <section id="async" class="section">
                <h2>⚡ Async Operations</h2>

                <h3>Async Reading</h3>
                <p>The library supports non-blocking I/O operations with Tokio:</p>

                <div class="code-block">
use noodles_gff::r#async;
use tokio::fs::File;
use tokio::io::BufReader;

#[tokio::main]
async fn main() -> Result&lt;(), Box&lt;dyn std::error::Error&gt;&gt; {
    // Open file asynchronously
    let file = File::open("annotations.gff3").await?;
    let mut reader = r#async::io::Reader::new(BufReader::new(file));

    // Read lines asynchronously
    let mut line = gff::Line::default();
    while reader.read_line(&mut line).await? != 0 {
        match line.kind() {
            gff::line::Kind::Record => {
                if let Some(result) = line.as_record() {
                    let record = result?;
                    println!("Async read: {}", record.ty());
                }
            }
            _ => {}
        }
    }

    Ok(())
}
                </div>

                <h3>Async Processing Pipeline</h3>
                <p>Combine async reading with stream processing:</p>

                <div class="code-block">
use futures::stream::{self, StreamExt};
use tokio::sync::mpsc;

async fn process_gff_async(filename: &str) -> Result&lt;(), Box&lt;dyn std::error::Error&gt;&gt; {
    let file = File::open(filename).await?;
    let mut reader = r#async::io::Reader::new(BufReader::new(file));

    // Create a channel for processing
    let (tx, mut rx) = mpsc::channel(100);

    // Spawn reader task
    let reader_task = tokio::spawn(async move {
        let mut line = gff::Line::default();
        while reader.read_line(&mut line).await.unwrap() != 0 {
            if let Some(Ok(record)) = line.as_record() {
                if tx.send(record).await.is_err() {
                    break;
                }
            }
        }
    });

    // Process records concurrently
    let processor_task = tokio::spawn(async move {
        while let Some(record) = rx.recv().await {
            // Process record asynchronously
            process_record_async(&record).await;
        }
    });

    // Wait for both tasks
    tokio::try_join!(reader_task, processor_task)?;

    Ok(())
}

async fn process_record_async(record: &gff::Record) {
    // Simulate async processing
    tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
    println!("Processed: {}", record.ty());
}
                </div>

                <div class="info">
                    <h4>💡 Async Benefits</h4>
                    <p>Async operations are particularly useful for:</p>
                    <ul>
                        <li>Processing large files without blocking</li>
                        <li>Concurrent I/O operations</li>
                        <li>Building responsive applications</li>
                        <li>Integrating with async web frameworks</li>
                    </ul>
                </div>
            </section>

            <!-- Examples Section -->
            <section id="examples" class="section">
                <h2>💡 Practical Examples</h2>

                <h3>Example 1: Extract Gene Hierarchy</h3>
                <div class="code-block">
use std::collections::HashMap;
use noodles_gff as gff;

#[derive(Debug)]
struct GeneHierarchy {
    gene: RecordBuf,
    transcripts: Vec&lt;TranscriptInfo&gt;,
}

#[derive(Debug)]
struct TranscriptInfo {
    transcript: RecordBuf,
    exons: Vec&lt;RecordBuf&gt;,
    cds: Vec&lt;RecordBuf&gt;,
}

fn extract_gene_hierarchy(filename: &str) -> Result&lt;Vec&lt;GeneHierarchy&gt;, Box&lt;dyn std::error::Error&gt;&gt; {
    let mut reader = File::open(filename)
        .map(BufReader::new)
        .map(gff::io::Reader::new)?;

    let mut genes = HashMap::new();
    let mut transcripts = HashMap::new();
    let mut features_by_parent = HashMap::new();

    // First pass: collect all features
    for result in reader.record_bufs() {
        let record = result?;
        let attributes = record.attributes();

        match record.ty().as_ref() {
            b"gene" => {
                if let Some(id) = attributes.get(b"ID") {
                    genes.insert(id.to_string(), record);
                }
            }
            b"transcript" | b"mRNA" => {
                if let Some(id) = attributes.get(b"ID") {
                    transcripts.insert(id.to_string(), record);
                }
            }
            _ => {
                if let Some(parent) = attributes.get(b"Parent") {
                    features_by_parent
                        .entry(parent.to_string())
                        .or_insert_with(Vec::new)
                        .push(record);
                }
            }
        }
    }

    // Second pass: build hierarchy
    let mut hierarchies = Vec::new();

    for (gene_id, gene) in genes {
        let mut gene_hierarchy = GeneHierarchy {
            gene,
            transcripts: Vec::new(),
        };

        // Find transcripts for this gene
        if let Some(transcript_features) = features_by_parent.get(&gene_id) {
            for transcript in transcript_features {
                if let Some(transcript_id) = transcript.attributes().get(b"ID") {
                    let transcript_id_str = transcript_id.to_string();

                    let mut transcript_info = TranscriptInfo {
                        transcript: transcript.clone(),
                        exons: Vec::new(),
                        cds: Vec::new(),
                    };

                    // Find exons and CDS for this transcript
                    if let Some(child_features) = features_by_parent.get(&transcript_id_str) {
                        for feature in child_features {
                            match feature.ty().as_ref() {
                                b"exon" => transcript_info.exons.push(feature.clone()),
                                b"CDS" => transcript_info.cds.push(feature.clone()),
                                _ => {}
                            }
                        }
                    }

                    gene_hierarchy.transcripts.push(transcript_info);
                }
            }
        }

        hierarchies.push(gene_hierarchy);
    }

    Ok(hierarchies)
}
                </div>

                <h3>Example 2: Convert GFF3 to BED Format</h3>
                <div class="code-block">
fn gff3_to_bed(input_file: &str, output_file: &str) -> Result&lt;(), Box&lt;dyn std::error::Error&gt;&gt; {
    let mut reader = File::open(input_file)
        .map(BufReader::new)
        .map(gff::io::Reader::new)?;

    let mut writer = File::create(output_file)
        .map(BufWriter::new)?;

    for result in reader.record_bufs() {
        let record = result?;

        // Only convert certain feature types
        if matches!(record.ty().as_ref(), b"gene" | b"exon" | b"CDS") {
            // Convert 1-based GFF3 coordinates to 0-based BED coordinates
            let start = usize::from(record.start()) - 1;
            let end = usize::from(record.end());

            // Get feature name from attributes
            let name = record.attributes()
                .get(b"Name")
                .or_else(|| record.attributes().get(b"ID"))
                .map(|v| v.to_string())
                .unwrap_or_else(|| "unknown".to_string());

            // Get score (default to 0 if missing)
            let score = record.score().unwrap_or(0.0) as i32;

            // Convert strand
            let strand = match record.strand() {
                gff::feature::record::Strand::Forward => "+",
                gff::feature::record::Strand::Reverse => "-",
                gff::feature::record::Strand::Unknown => ".",
            };

            // Write BED format: chr start end name score strand
            writeln!(writer, "{}\t{}\t{}\t{}\t{}\t{}",
                record.reference_sequence_name(),
                start,
                end,
                name,
                score,
                strand
            )?;
        }
    }

    Ok(())
}
                </div>

                <h3>Example 3: Feature Statistics</h3>
                <div class="code-block">
use std::collections::HashMap;

#[derive(Debug, Default)]
struct FeatureStats {
    count: usize,
    total_length: u64,
    min_length: Option&lt;u64&gt;,
    max_length: Option&lt;u64&gt;,
    stranded_counts: HashMap&lt;String, usize&gt;,
}

fn analyze_features(filename: &str) -> Result&lt;HashMap&lt;String, FeatureStats&gt;, Box&lt;dyn std::error::Error&gt;&gt; {
    let mut reader = File::open(filename)
        .map(BufReader::new)
        .map(gff::io::Reader::new)?;

    let mut stats = HashMap::new();

    for result in reader.record_bufs() {
        let record = result?;

        let feature_type = record.ty().to_string();
        let length = (usize::from(record.end()) - usize::from(record.start()) + 1) as u64;
        let strand = format!("{:?}", record.strand());

        let feature_stats = stats.entry(feature_type).or_insert_with(FeatureStats::default);

        feature_stats.count += 1;
        feature_stats.total_length += length;

        feature_stats.min_length = Some(
            feature_stats.min_length.map_or(length, |min| min.min(length))
        );
        feature_stats.max_length = Some(
            feature_stats.max_length.map_or(length, |max| max.max(length))
        );

        *feature_stats.stranded_counts.entry(strand).or_insert(0) += 1;
    }

    // Print statistics
    for (feature_type, stats) in &stats {
        println!("Feature type: {}", feature_type);
        println!("  Count: {}", stats.count);
        println!("  Total length: {}", stats.total_length);
        println!("  Average length: {:.2}", stats.total_length as f64 / stats.count as f64);
        println!("  Min length: {:?}", stats.min_length);
        println!("  Max length: {:?}", stats.max_length);
        println!("  Strand distribution: {:?}", stats.stranded_counts);
        println!();
    }

    Ok(stats)
}
                </div>
            </section>

            <!-- Best Practices Section -->
            <section id="best-practices" class="section">
                <h2>🎯 Best Practices</h2>

                <h3>Performance Optimization</h3>
                <div class="highlight">
                    <h4>Memory Efficiency</h4>
                    <ul>
                        <li>Use <code>BufReader</code> for large files to reduce I/O overhead</li>
                        <li>Prefer zero-copy <code>Record</code> over <code>RecordBuf</code> when possible</li>
                        <li>Process records in streaming fashion rather than loading all into memory</li>
                        <li>Use indexed queries for random access patterns</li>
                        <li>Consider async operations for concurrent processing</li>
                    </ul>
                </div>

                <h3>Error Handling</h3>
                <div class="code-block">
// Robust error handling pattern
match reader.read_line(&mut line) {
    Ok(0) => break, // EOF
    Ok(_) => {
        match line.kind() {
            gff::line::Kind::Directive => {
                // Process directive
                if let Some(directive) = line.as_directive() {
                    process_directive(&directive)?;
                }
            }
            gff::line::Kind::Comment => {
                // Skip comments or log them
            }
            gff::line::Kind::Record => {
                match line.as_record() {
                    Some(Ok(record)) => {
                        // Process valid record
                        process_record(&record)?;
                    }
                    Some(Err(e)) => {
                        eprintln!("Invalid record: {}", e);
                        // Continue processing or abort based on requirements
                    }
                    None => unreachable!(),
                }
            }
        }
    }
    Err(e) => {
        eprintln!("I/O error: {}", e);
        return Err(e.into());
    }
}
                </div>

                <h3>Data Validation</h3>
                <div class="warning">
                    <h4>⚠️ Common Pitfalls</h4>
                    <ul>
                        <li>Always validate coordinates before arithmetic operations</li>
                        <li>Handle missing attributes gracefully</li>
                        <li>Be aware of 1-based coordinate system</li>
                        <li>Check for URL-encoded attribute values</li>
                        <li>Validate Parent-Child relationships in hierarchical features</li>
                        <li>Handle different feature type naming conventions</li>
                    </ul>
                </div>

                <h3>Testing Strategies</h3>
                <div class="code-block">
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_gff3_parsing() {
        let gff3_data = b"##gff-version 3\nchr1\tHAVANA\tgene\t11869\t14409\t.\t+\t.\tID=gene:ENSG00000223972;Name=DDX11L1\n";
        let mut reader = gff::io::Reader::new(&gff3_data[..]);

        let mut line = gff::Line::default();

        // Read directive
        assert!(reader.read_line(&mut line).unwrap() > 0);
        assert_eq!(line.kind(), gff::line::Kind::Directive);

        // Read record
        assert!(reader.read_line(&mut line).unwrap() > 0);
        assert_eq!(line.kind(), gff::line::Kind::Record);

        let record = line.as_record().unwrap().unwrap();
        assert_eq!(record.reference_sequence_name(), "chr1");
        assert_eq!(record.ty(), "gene");

        let attributes = record.attributes();
        assert!(attributes.get(b"ID").is_some());
        assert!(attributes.get(b"Name").is_some());
    }

    #[test]
    fn test_feature_hierarchy() {
        // Test parent-child relationships
        let parent_id = "gene:ENSG00000223972";
        let child_parent = "gene:ENSG00000223972";
        assert_eq!(parent_id, child_parent);
    }
}
                </div>
            </section>

            <!-- Visualization Section -->
            <section id="visualization" class="section">
                <h2>📊 Data Flow Visualization</h2>

                <h3>GFF3 Processing Pipeline</h3>
                <p>Understanding the data flow through the noodles-gff library:</p>

                <div style="text-align: center; margin: 2rem 0;">
                    <svg width="1000" height="700" viewBox="0 0 1000 700" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="1000" height="700" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                        <!-- Title -->
                        <text x="500" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">GFF3 Data Processing Flow</text>

                        <!-- Input Layer -->
                        <rect x="50" y="70" width="900" height="80" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="8"/>
                        <text x="500" y="95" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Input Sources</text>

                        <!-- Input types -->
                        <rect x="80" y="110" width="130" height="30" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="4"/>
                        <text x="145" y="128" text-anchor="middle" font-size="10" fill="white">GFF3 Files</text>

                        <rect x="230" y="110" width="130" height="30" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="4"/>
                        <text x="295" y="128" text-anchor="middle" font-size="10" fill="white">Compressed GFF3</text>

                        <rect x="380" y="110" width="130" height="30" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="4"/>
                        <text x="445" y="128" text-anchor="middle" font-size="10" fill="white">Streams</text>

                        <rect x="530" y="110" width="130" height="30" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="4"/>
                        <text x="595" y="128" text-anchor="middle" font-size="10" fill="white">Memory Buffers</text>

                        <rect x="680" y="110" width="130" height="30" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="4"/>
                        <text x="745" y="128" text-anchor="middle" font-size="10" fill="white">Indexed Files</text>

                        <rect x="830" y="110" width="100" height="30" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="4"/>
                        <text x="880" y="128" text-anchor="middle" font-size="10" fill="white">Async Sources</text>

                        <!-- Reader Layer -->
                        <rect x="50" y="180" width="900" height="60" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="8"/>
                        <text x="500" y="205" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Reader Layer</text>
                        <text x="500" y="225" text-anchor="middle" font-size="12" fill="white">gff::io::Reader - Line parsing, buffering, decompression, async support</text>

                        <!-- Parsing Layer -->
                        <rect x="50" y="270" width="900" height="100" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="8"/>
                        <text x="500" y="295" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Parsing Layer</text>

                        <!-- Parsing components -->
                        <rect x="80" y="310" width="120" height="30" fill="#a569bd" stroke="#9b59b6" stroke-width="1" rx="4"/>
                        <text x="140" y="328" text-anchor="middle" font-size="9" fill="white">Line Classification</text>

                        <rect x="220" y="310" width="120" height="30" fill="#a569bd" stroke="#9b59b6" stroke-width="1" rx="4"/>
                        <text x="280" y="328" text-anchor="middle" font-size="9" fill="white">Directive Parsing</text>

                        <rect x="360" y="310" width="120" height="30" fill="#a569bd" stroke="#9b59b6" stroke-width="1" rx="4"/>
                        <text x="420" y="328" text-anchor="middle" font-size="9" fill="white">Field Extraction</text>

                        <rect x="500" y="310" width="120" height="30" fill="#a569bd" stroke="#9b59b6" stroke-width="1" rx="4"/>
                        <text x="560" y="328" text-anchor="middle" font-size="9" fill="white">Attribute Parsing</text>

                        <rect x="640" y="310" width="120" height="30" fill="#a569bd" stroke="#9b59b6" stroke-width="1" rx="4"/>
                        <text x="700" y="328" text-anchor="middle" font-size="9" fill="white">URL Decoding</text>

                        <rect x="780" y="310" width="120" height="30" fill="#a569bd" stroke="#9b59b6" stroke-width="1" rx="4"/>
                        <text x="840" y="328" text-anchor="middle" font-size="9" fill="white">Type Validation</text>

                        <!-- Record Layer -->
                        <rect x="50" y="400" width="900" height="80" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="8"/>
                        <text x="500" y="425" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Record Representation</text>

                        <!-- Record types -->
                        <rect x="100" y="445" width="150" height="25" fill="#f4d03f" stroke="#f39c12" stroke-width="1" rx="3"/>
                        <text x="175" y="460" text-anchor="middle" font-size="9" fill="black">DirectiveBuf</text>

                        <rect x="270" y="445" width="150" height="25" fill="#f4d03f" stroke="#f39c12" stroke-width="1" rx="3"/>
                        <text x="345" y="460" text-anchor="middle" font-size="9" fill="black">RecordBuf (owned)</text>

                        <rect x="440" y="445" width="150" height="25" fill="#f4d03f" stroke="#f39c12" stroke-width="1" rx="3"/>
                        <text x="515" y="460" text-anchor="middle" font-size="9" fill="black">Record (zero-copy)</text>

                        <rect x="610" y="445" width="150" height="25" fill="#f4d03f" stroke="#f39c12" stroke-width="1" rx="3"/>
                        <text x="685" y="460" text-anchor="middle" font-size="9" fill="black">Attributes</text>

                        <rect x="780" y="445" width="120" height="25" fill="#f4d03f" stroke="#f39c12" stroke-width="1" rx="3"/>
                        <text x="840" y="460" text-anchor="middle" font-size="9" fill="black">LineBuf</text>

                        <!-- Processing Layer -->
                        <rect x="50" y="510" width="900" height="100" fill="#27ae60" stroke="#229954" stroke-width="2" rx="8"/>
                        <text x="500" y="535" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Application Processing</text>

                        <!-- Processing types -->
                        <rect x="80" y="555" width="100" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="130" y="570" text-anchor="middle" font-size="9" fill="white">Filtering</text>

                        <rect x="200" y="555" width="100" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="250" y="570" text-anchor="middle" font-size="9" fill="white">Hierarchy Building</text>

                        <rect x="320" y="555" width="100" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="370" y="570" text-anchor="middle" font-size="9" fill="white">Transformation</text>

                        <rect x="440" y="555" width="100" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="490" y="570" text-anchor="middle" font-size="9" fill="white">Analysis</text>

                        <rect x="560" y="555" width="100" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="610" y="570" text-anchor="middle" font-size="9" fill="white">Querying</text>

                        <rect x="680" y="555" width="100" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="730" y="570" text-anchor="middle" font-size="9" fill="white">Validation</text>

                        <rect x="800" y="555" width="100" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="850" y="570" text-anchor="middle" font-size="9" fill="white">Async Processing</text>

                        <!-- Arrows -->
                        <defs>
                            <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                            </marker>
                        </defs>

                        <!-- Vertical flow arrows -->
                        <line x1="500" y1="150" x2="500" y2="175" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>
                        <line x1="500" y1="240" x2="500" y2="265" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>
                        <line x1="500" y1="370" x2="500" y2="395" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>
                        <line x1="500" y1="480" x2="500" y2="505" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>

                        <!-- Feature highlights -->
                        <rect x="50" y="630" width="900" height="50" fill="#34495e" stroke="#2c3e50" stroke-width="2" rx="5"/>
                        <text x="500" y="650" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Key GFF3 Features</text>
                        <text x="150" y="665" text-anchor="middle" font-size="10" fill="white">Hierarchical features</text>
                        <text x="300" y="665" text-anchor="middle" font-size="10" fill="white">Directive support</text>
                        <text x="450" y="665" text-anchor="middle" font-size="10" fill="white">URL encoding</text>
                        <text x="600" y="665" text-anchor="middle" font-size="10" fill="white">Zero-copy parsing</text>
                        <text x="750" y="665" text-anchor="middle" font-size="10" fill="white">Async operations</text>
                        <text x="850" y="665" text-anchor="middle" font-size="10" fill="white">Type safety</text>
                    </svg>
                </div>

                <h3>GFF3 vs GTF Comparison</h3>
                <div style="text-align: center; margin: 2rem 0;">
                    <svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">GFF3 vs GTF Format Comparison</text>

                        <!-- GFF3 Side -->
                        <rect x="50" y="50" width="300" height="320" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="8"/>
                        <text x="200" y="75" text-anchor="middle" font-size="14" font-weight="bold" fill="white">GFF3 Format</text>

                        <!-- GFF3 Features -->
                        <rect x="70" y="90" width="260" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="200" y="105" text-anchor="middle" font-size="10" fill="white">Hierarchical relationships (ID/Parent)</text>

                        <rect x="70" y="125" width="260" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="200" y="140" text-anchor="middle" font-size="10" fill="white">Flexible attribute system</text>

                        <rect x="70" y="160" width="260" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="200" y="175" text-anchor="middle" font-size="10" fill="white">URL encoding for special chars</text>

                        <rect x="70" y="195" width="260" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="200" y="210" text-anchor="middle" font-size="10" fill="white">Directives for metadata</text>

                        <rect x="70" y="230" width="260" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="200" y="245" text-anchor="middle" font-size="10" fill="white">Embedded FASTA sequences</text>

                        <rect x="70" y="265" width="260" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="200" y="280" text-anchor="middle" font-size="10" fill="white">Multiple feature types</text>

                        <rect x="70" y="300" width="260" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="200" y="315" text-anchor="middle" font-size="10" fill="white">Standardized by SO</text>

                        <rect x="70" y="335" width="260" height="25" fill="#2ecc71" stroke="#27ae60" stroke-width="1" rx="3"/>
                        <text x="200" y="350" text-anchor="middle" font-size="10" fill="white">Complex feature relationships</text>

                        <!-- GTF Side -->
                        <rect x="450" y="50" width="300" height="320" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="8"/>
                        <text x="600" y="75" text-anchor="middle" font-size="14" font-weight="bold" fill="white">GTF Format</text>

                        <!-- GTF Features -->
                        <rect x="470" y="90" width="260" height="25" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
                        <text x="600" y="105" text-anchor="middle" font-size="10" fill="white">Gene-centric structure</text>

                        <rect x="470" y="125" width="260" height="25" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
                        <text x="600" y="140" text-anchor="middle" font-size="10" fill="white">Required gene_id/transcript_id</text>

                        <rect x="470" y="160" width="260" height="25" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
                        <text x="600" y="175" text-anchor="middle" font-size="10" fill="white">Simpler attribute format</text>

                        <rect x="470" y="195" width="260" height="25" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
                        <text x="600" y="210" text-anchor="middle" font-size="10" fill="white">Escape sequences</text>

                        <rect x="470" y="230" width="260" height="25" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
                        <text x="600" y="245" text-anchor="middle" font-size="10" fill="white">RNA-seq analysis focus</text>

                        <rect x="470" y="265" width="260" height="25" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
                        <text x="600" y="280" text-anchor="middle" font-size="10" fill="white">Limited feature types</text>

                        <rect x="470" y="300" width="260" height="25" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
                        <text x="600" y="315" text-anchor="middle" font-size="10" fill="white">Widely supported</text>

                        <rect x="470" y="335" width="260" height="25" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
                        <text x="600" y="350" text-anchor="middle" font-size="10" fill="white">Simpler relationships</text>
                    </svg>
                </div>

                <h3>Conclusion</h3>
                <p>The <code>noodles-gff</code> library provides a comprehensive, efficient, and safe way to work with GFF3 files in Rust. Its design emphasizes performance through zero-copy parsing, safety through Rust's type system, and usability through a rich API that covers all common GFF3 processing tasks including hierarchical features, directives, and async operations.</p>

                <div class="info">
                    <h4>🚀 Next Steps</h4>
                    <p>To get started with noodles-gff:</p>
                    <ol>
                        <li>Add <code>noodles-gff = "0.51"</code> to your Cargo.toml</li>
                        <li>For async support: <code>noodles-gff = { version = "0.51", features = ["async"] }</code></li>
                        <li>Explore the examples in the repository</li>
                        <li>Check the API documentation for detailed method signatures</li>
                        <li>Join the community for support and contributions</li>
                        <li>Consider the differences between GFF3 and GTF for your use case</li>
                    </ol>
                </div>
            </section>
        </div>
    </div>

    <script>
        // Simulate loading progress
        let progress = 0;
        const progressBar = document.getElementById('progress');
        const loadingDiv = document.getElementById('loading');
        const contentDiv = document.getElementById('content');

        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
                setTimeout(() => {
                    loadingDiv.style.display = 'none';
                    contentDiv.style.display = 'block';
                    loadContent();
                }, 500);
            }
            progressBar.style.width = progress + '%';
        }, 200);

        function loadContent() {
            // Add smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add copy functionality to code blocks
            document.querySelectorAll('.code-block').forEach(block => {
                const copyButton = document.createElement('button');
                copyButton.textContent = '📋 Copy';
                copyButton.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    background: #3498db;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                    z-index: 10;
                `;

                block.style.position = 'relative';
                block.appendChild(copyButton);

                copyButton.addEventListener('click', () => {
                    const text = block.textContent.replace('📋 Copy', '').trim();
                    navigator.clipboard.writeText(text).then(() => {
                        copyButton.textContent = '✅ Copied!';
                        setTimeout(() => {
                            copyButton.textContent = '📋 Copy';
                        }, 2000);
                    });
                });
            });

            // Add section highlighting on scroll
            const sections = document.querySelectorAll('.section');
            const tocLinks = document.querySelectorAll('.toc a');

            function highlightCurrentSection() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (window.scrollY >= sectionTop - 100) {
                        current = section.getAttribute('id');
                    }
                });

                tocLinks.forEach(link => {
                    link.style.color = '';
                    link.style.fontWeight = '';
                    if (link.getAttribute('href') === '#' + current) {
                        link.style.color = '#e74c3c';
                        link.style.fontWeight = 'bold';
                    }
                });
            }

            window.addEventListener('scroll', highlightCurrentSection);
            highlightCurrentSection(); // Initial call

            // Add interactive elements
            console.log('🧬 Noodles GFF Tutorial loaded successfully!');
            console.log('📚 Navigate using the table of contents');
            console.log('📋 Click copy buttons on code blocks to copy examples');
            console.log('🔍 This tutorial covers GFF3 format handling with noodles-gff');
        }

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K to focus search (if we had search)
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                // Could implement search functionality here
            }

            // Escape to scroll to top
            if (e.key === 'Escape') {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        });

        // Add print styles
        const printStyles = `
            @media print {
                .toc { page-break-after: always; }
                .section { page-break-inside: avoid; }
                .code-block { page-break-inside: avoid; }
                svg { max-width: 100%; height: auto; }
                .feature-grid { display: block; }
                .feature-card { margin-bottom: 1rem; }
            }
        `;

        const styleSheet = document.createElement('style');
        styleSheet.textContent = printStyles;
        document.head.appendChild(styleSheet);
    </script>
</body>
</html>
