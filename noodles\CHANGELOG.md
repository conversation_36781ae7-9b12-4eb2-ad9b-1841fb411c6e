# Changelog

## 0.100.0 - 2025-07-12

  * [noodles-bam 0.82.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.82.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.77.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.77.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.27.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.27.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.42.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.42.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.18.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.85.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.85.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.50.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.50.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.55.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.55.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.20.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.20.0/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.51.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.51.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.46.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.46.0/noodles-gtf/CHANGELOG.md)
  * [noodles-htsget 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-htsget-0.9.0/noodles-htsget/CHANGELOG.md)
  * [noodles-refget 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-refget-0.8.0/noodles-refget/CHANGELOG.md)
  * [noodles-sam 0.78.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.78.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.56.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.56.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.69.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.69.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.80.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.80.0/noodles-vcf/CHANGELOG.md)

## 0.99.0 - 2025-05-29

  * [noodles-bam 0.81.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.81.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.76.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.76.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.26.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.26.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.41.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.41.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.84.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.84.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.49.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.49.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.54.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.54.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.19.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.19.0/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.50.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.50.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.45.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.45.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.77.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.77.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.55.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.55.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.68.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.68.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.79.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.79.0/noodles-vcf/CHANGELOG.md)

## 0.98.0 - 2025-05-16

  * [noodles-bam 0.80.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.80.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.75.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.75.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.25.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.25.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.40.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.40.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.83.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.83.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.48.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.48.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.53.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.53.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.49.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.49.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.44.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.44.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.76.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.76.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.54.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.54.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.67.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.67.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.78.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.78.0/noodles-vcf/CHANGELOG.md)

## 0.97.0 - 2025-04-13

  * [noodles-bam 0.79.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.79.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.74.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.74.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.24.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.24.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.39.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.39.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.82.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.82.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.47.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.47.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.52.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.52.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.48.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.48.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.43.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.43.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.75.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.75.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.53.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.53.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.66.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.66.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.77.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.77.0/noodles-vcf/CHANGELOG.md)

## 0.96.0 - 2025-04-06

  * [noodles-bam 0.78.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.78.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.73.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.73.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.23.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.23.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.38.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.38.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.81.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.81.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.46.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.46.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.51.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.51.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.47.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.47.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.42.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.42.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.74.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.74.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.52.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.52.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.65.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.65.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.76.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.76.0/noodles-vcf/CHANGELOG.md)

## 0.95.0 - 2025-03-20

  * [noodles-gff 0.46.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.46.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.41.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.41.0/noodles-gtf/CHANGELOG.md)

## 0.94.0 - 2025-03-08

  * [noodles-bam 0.77.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.77.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.72.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.72.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.22.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.22.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.37.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.37.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.17.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.80.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.80.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.45.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.45.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.50.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.50.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.18.0/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.45.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.45.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.40.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.40.0/noodles-gtf/CHANGELOG.md)
  * [noodles-htsget 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-htsget-0.8.0/noodles-htsget/CHANGELOG.md)
  * [noodles-refget 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-refget-0.7.0/noodles-refget/CHANGELOG.md)
  * [noodles-sam 0.73.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.73.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.51.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.51.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.64.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.64.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.75.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.75.0/noodles-vcf/CHANGELOG.md)

## 0.93.0 - 2025-02-20

  * [noodles-cram 0.79.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.79.0/noodles-cram/CHANGELOG.md)
  * [noodles-util 0.63.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.63.0/noodles-util/CHANGELOG.md)

## 0.92.0 - 2025-02-17

  * [noodles-bcf 0.71.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.71.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.78.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.78.0/noodles-cram/CHANGELOG.md)
  * [noodles-util 0.62.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.62.0/noodles-util/CHANGELOG.md)

## 0.91.0 - 2025-02-06

  * [noodles-bam 0.76.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.76.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.70.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.70.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.21.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.21.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.36.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.36.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.77.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.77.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.44.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.44.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.49.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.49.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.44.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.44.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.39.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.39.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.72.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.72.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.50.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.50.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.61.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.61.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.74.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.74.0/noodles-vcf/CHANGELOG.md)

## 0.90.0 - 2025-01-24

  * [noodles-bam 0.75.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.75.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.69.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.69.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.76.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.76.0/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.48.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.48.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gtf 0.38.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.38.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.71.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.71.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.60.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.60.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.73.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.73.0/noodles-vcf/CHANGELOG.md)

## 0.89.0 - 2025-01-23

  * [noodles-bam 0.74.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.74.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.68.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.68.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.20.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.20.0/noodles-bed/CHANGELOG.md)
  * [noodles-cram 0.75.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.75.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.43.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.43.0/noodles-csi/CHANGELOG.md)
  * [noodles-gff 0.43.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.43.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.37.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.37.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.70.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.70.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.49.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.49.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.59.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.59.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.72.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.72.0/noodles-vcf/CHANGELOG.md)

## 0.88.0 - 2025-01-19

  * [noodles-bam 0.73.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.73.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.67.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.67.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.19.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.19.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.35.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.35.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.16.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.74.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.74.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.42.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.42.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.47.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.47.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.17.0/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.42.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.42.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.36.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.36.0/noodles-gtf/CHANGELOG.md)
  * [noodles-htsget 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-htsget-0.7.0/noodles-htsget/CHANGELOG.md)
  * [noodles-refget 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-refget-0.6.0/noodles-refget/CHANGELOG.md)
  * [noodles-sam 0.69.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.69.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.48.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.48.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.58.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.58.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.71.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.71.0/noodles-vcf/CHANGELOG.md)

## 0.87.0 - 2024-12-20

  * [noodles-bam 0.72.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.72.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.66.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.66.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.73.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.73.0/noodles-cram/CHANGELOG.md)
  * [noodles-gff 0.41.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.41.0/noodles-gff/CHANGELOG.md)
  * [noodles-sam 0.68.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.68.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.57.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.57.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.70.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.70.0/noodles-vcf/CHANGELOG.md)

## 0.86.0 - 2024-12-12

  * [noodles-bam 0.71.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.71.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.65.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.65.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.18.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.34.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.34.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.72.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.72.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.41.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.41.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.46.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.46.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.40.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.40.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.35.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.35.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.67.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.67.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.47.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.47.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.56.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.56.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.69.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.69.0/noodles-vcf/CHANGELOG.md)

## 0.85.0 - 2024-11-07

  * [noodles-bam 0.70.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.70.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.64.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.64.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.71.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.71.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.40.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.40.0/noodles-csi/CHANGELOG.md)
  * [noodles-gff 0.39.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.39.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.34.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.34.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.66.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.66.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.46.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.46.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.55.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.55.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.68.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.68.0/noodles-vcf/CHANGELOG.md)

## 0.84.0 - 2024-10-22

  * [noodles-bam 0.69.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.69.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.63.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.63.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.70.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.70.0/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.45.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.45.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.16.0/noodles-fastq/CHANGELOG.md)
  * [noodles-util 0.54.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.54.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.67.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.67.0/noodles-vcf/CHANGELOG.md)

## 0.83.0 - 2024-09-26

  * [noodles-bam 0.68.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.68.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.62.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.62.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.17.0/noodles-bed/CHANGELOG.md)
  * [noodles-cram 0.69.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.69.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.39.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.39.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.44.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.44.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.15.0/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.38.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.38.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.33.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.33.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.65.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.65.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.45.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.45.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.53.1](https://github.com/zaeleus/noodles/blob/noodles-util-0.53.1/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.66.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.66.0/noodles-vcf/CHANGELOG.md)

## 0.82.0 - 2024-09-12

  * [noodles-bcf 0.61.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.61.0/noodles-bcf/CHANGELOG.md)
  * [noodles-util 0.53.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.53.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.65.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.65.0/noodles-vcf/CHANGELOG.md)

## 0.81.0 - 2024-09-09

  * [noodles-bcf 0.60.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.60.0/noodles-bcf/CHANGELOG.md)
  * [noodles-gff 0.37.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.37.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.32.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.32.0/noodles-gtf/CHANGELOG.md)
  * [noodles-util 0.52.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.52.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.64.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.64.0/noodles-vcf/CHANGELOG.md)

## 0.80.0 - 2024-09-04

  * [noodles-bam 0.67.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.67.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.59.1](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.59.1/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.16.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.33.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.33.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.68.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.68.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.38.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.38.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.43.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.43.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.36.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.36.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.31.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.31.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.64.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.64.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.44.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.44.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.51.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.51.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.63.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.63.0/noodles-vcf/CHANGELOG.md)

## 0.79.0 - 2024-08-04

  * [noodles-bam 0.66.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.66.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.59.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.59.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.67.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.67.0/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.42.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.42.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.14.0/noodles-fastq/CHANGELOG.md)
  * [noodles-sam 0.63.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.63.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.50.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.50.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.62.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.62.0/noodles-vcf/CHANGELOG.md)

## 0.78.0 - 2024-07-14

  * [noodles-bam 0.65.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.65.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.58.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.58.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.32.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.32.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.66.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.66.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.37.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.37.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.41.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.41.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.13.0/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.35.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.35.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.30.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.30.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.62.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.62.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.43.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.43.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.49.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.49.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.61.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.61.0/noodles-vcf/CHANGELOG.md)

## 0.77.0 - 2024-06-17

  * [noodles-bam 0.64.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.64.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.57.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.57.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.15.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.31.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.31.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.65.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.65.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.36.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.36.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.40.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.40.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.12.0/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.34.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.34.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.29.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.29.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.61.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.61.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.42.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.42.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.48.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.48.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.60.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.60.0/noodles-vcf/CHANGELOG.md)

## 0.76.0 - 2024-06-06

  * [noodles-bcf 0.56.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.56.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.14.0/noodles-bed/CHANGELOG.md)
  * [noodles-util 0.47.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.47.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.59.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.59.0/noodles-vcf/CHANGELOG.md)

## 0.75.0 - 2024-05-31

  * [noodles-bcf 0.55.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.55.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.64.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.64.0/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.39.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.39.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.11.0/noodles-fastq/CHANGELOG.md)
  * [noodles-util 0.46.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.46.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.58.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.58.0/noodles-vcf/CHANGELOG.md)

## 0.74.0 - 2024-05-19

  * [noodles-gff 0.33.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.33.0/noodles-gff/CHANGELOG.md)

## 0.73.0 - 2024-05-16

  * [noodles-bam 0.63.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.63.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.54.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.54.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.30.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.30.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.63.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.63.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.35.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.35.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.38.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.38.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.32.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.32.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.28.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.28.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.60.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.60.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.41.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.41.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.45.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.45.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.57.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.57.0/noodles-vcf/CHANGELOG.md)

## 0.72.0 - 2024-05-08

  * [noodles-bam 0.62.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.62.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.53.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.53.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.13.0/noodles-bed/CHANGELOG.md)
  * [noodles-core 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.15.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.62.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.62.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.34.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.34.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.37.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.37.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.31.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.31.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.27.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.27.0/noodles-gtf/CHANGELOG.md)
  * [noodles-htsget 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-htsget-0.6.0/noodles-htsget/CHANGELOG.md)
  * [noodles-refget 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-refget-0.5.0/noodles-refget/CHANGELOG.md)
  * [noodles-sam 0.59.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.59.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.40.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.40.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.44.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.44.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.56.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.56.0/noodles-vcf/CHANGELOG.md)

## 0.71.0 - 2024-05-02

  * [noodles-bam 0.61.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.61.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.52.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.52.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.29.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.29.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.61.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.61.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.33.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.33.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.36.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.36.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.30.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.30.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.26.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.26.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.58.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.58.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.39.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.39.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.43.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.43.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.55.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.55.0/noodles-vcf/CHANGELOG.md)

## 0.70.0 - 2024-04-22

  * [noodles-bam 0.60.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.60.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.51.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.51.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.60.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.60.0/noodles-cram/CHANGELOG.md)
  * [noodles-sam 0.57.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.57.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.42.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.42.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.54.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.54.0/noodles-vcf/CHANGELOG.md)

## 0.69.0 - 2024-04-11

  * [noodles-bcf 0.50.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.50.0/noodles-bcf/CHANGELOG.md)
  * [noodles-util 0.41.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.41.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.53.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.53.0/noodles-vcf/CHANGELOG.md)

## 0.68.0 - 2024-04-04

  * [noodles-bam 0.59.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.59.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.49.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.49.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.59.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.59.0/noodles-cram/CHANGELOG.md)
  * [noodles-htsget 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-htsget-0.5.0/noodles-htsget/CHANGELOG.md)
  * [noodles-refget 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-refget-0.4.0/noodles-refget/CHANGELOG.md)
  * [noodles-sam 0.56.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.56.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.40.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.40.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.52.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.52.0/noodles-vcf/CHANGELOG.md)

## 0.67.0 - 2024-03-28

  * [noodles-bam 0.58.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.58.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.48.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.48.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.28.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.28.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.58.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.58.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.32.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.32.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.35.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.35.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.29.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.29.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.25.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.25.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.55.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.55.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.38.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.38.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.39.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.39.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.51.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.51.0/noodles-vcf/CHANGELOG.md)

## 0.66.0 - 2024-03-12

  * [noodles-bam 0.57.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.57.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.47.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.47.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.27.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.27.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.57.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.57.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.31.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.31.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.34.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.34.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.28.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.28.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.24.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.24.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.54.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.54.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.37.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.37.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.38.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.38.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.50.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.50.0/noodles-vcf/CHANGELOG.md)

## 0.65.0 - 2024-02-22

  * [noodles-cram 0.56.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.56.0/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.33.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.33.0/noodles-fasta/CHANGELOG.md)
  * [noodles-util 0.37.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.37.0/noodles-util/CHANGELOG.md)

## 0.64.0 - 2024-02-15

  * [noodles-bam 0.56.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.56.0/noodles-bam/CHANGELOG.md)
  * [noodles-cram 0.55.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.55.0/noodles-cram/CHANGELOG.md)
  * [noodles-sam 0.53.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.53.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.36.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.36.0/noodles-util/CHANGELOG.md)

## 0.63.0 - 2024-02-08

  * [noodles-bam 0.55.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.55.0/noodles-bam/CHANGELOG.md)
  * [noodles-cram 0.54.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.54.0/noodles-cram/CHANGELOG.md)
  * [noodles-sam 0.52.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.52.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.35.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.35.0/noodles-util/CHANGELOG.md)

## 0.62.1 - 2024-02-04

  * [noodles-bam 0.54.1](https://github.com/zaeleus/noodles/blob/noodles-bam-0.54.1/noodles-bam/CHANGELOG.md)
  * [noodles-cram 0.53.1](https://github.com/zaeleus/noodles/blob/noodles-cram-0.53.1/noodles-cram/CHANGELOG.md)
  * [noodles-util 0.34.1](https://github.com/zaeleus/noodles/blob/noodles-util-0.34.1/noodles-util/CHANGELOG.md)

## 0.62.0 - 2024-02-01

  * [noodles-bam 0.54.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.54.0/noodles-bam/CHANGELOG.md)
  * [noodles-cram 0.53.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.53.0/noodles-cram/CHANGELOG.md)
  * [noodles-sam 0.51.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.51.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.34.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.34.0/noodles-util/CHANGELOG.md)

## 0.61.0 - 2024-01-25

  * [noodles-bam 0.53.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.53.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.46.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.46.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.12.0/noodles-bed/CHANGELOG.md)
  * [noodles-core 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.14.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.52.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.52.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.30.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.30.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.32.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.32.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.27.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.27.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.23.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.23.0/noodles-gtf/CHANGELOG.md)
  * [noodles-htsget 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-htsget-0.4.0/noodles-htsget/CHANGELOG.md)
  * [noodles-refget 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-refget-0.3.0/noodles-refget/CHANGELOG.md)
  * [noodles-sam 0.50.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.50.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.36.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.36.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.33.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.33.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.49.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.49.0/noodles-vcf/CHANGELOG.md)

## 0.60.0 - 2023-12-14

  * [noodles-bam 0.52.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.52.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.45.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.45.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.11.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.26.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.26.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.13.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.51.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.51.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.29.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.29.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.31.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.31.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.10.0/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.26.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.26.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.22.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.22.0/noodles-gtf/CHANGELOG.md)
  * [noodles-htsget 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-htsget-0.3.0/noodles-htsget/CHANGELOG.md)
  * [noodles-refget 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-refget-0.2.0/noodles-refget/CHANGELOG.md)
  * [noodles-sam 0.49.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.49.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.35.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.35.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.32.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.32.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.48.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.48.0/noodles-vcf/CHANGELOG.md)

## 0.59.0 - 2023-11-14

  * [noodles-bam 0.51.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.51.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.44.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.44.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.50.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.50.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.28.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.28.0/noodles-csi/CHANGELOG.md)
  * [noodles-gff 0.25.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.25.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.21.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.21.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.48.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.48.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.34.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.34.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.31.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.31.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.47.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.47.0/noodles-vcf/CHANGELOG.md)

## 0.58.0 - 2023-11-13

  * [noodles-bam 0.50.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.50.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.43.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.43.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.49.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.49.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.27.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.27.0/noodles-csi/CHANGELOG.md)
  * [noodles-gff 0.24.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.24.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.20.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.20.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.47.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.47.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.33.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.33.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.30.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.30.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.46.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.46.0/noodles-vcf/CHANGELOG.md)

## 0.57.0 - 2023-11-02

  * [noodles-bam 0.49.1](https://github.com/zaeleus/noodles/blob/noodles-bam-0.49.1/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.42.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.42.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.48.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.48.0/noodles-cram/CHANGELOG.md)
  * [noodles-util 0.29.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.29.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.45.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.45.0/noodles-vcf/CHANGELOG.md)

## 0.56.0 - 2023-10-26

  * [noodles-bam 0.49.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.49.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.41.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.41.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.47.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.47.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.26.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.26.0/noodles-csi/CHANGELOG.md)
  * [noodles-gff 0.23.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.23.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.19.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.19.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.46.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.46.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.32.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.32.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.28.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.28.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.44.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.44.0/noodles-vcf/CHANGELOG.md)

## 0.55.0 - 2023-10-23

  * [noodles-bcf 0.40.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.40.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.46.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.46.0/noodles-cram/CHANGELOG.md)
  * [noodles-util 0.27.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.27.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.43.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.43.0/noodles-vcf/CHANGELOG.md)

## 0.54.0 - 2023-10-19

  * [noodles-bam 0.48.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.48.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.39.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.39.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.45.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.45.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.25.1](https://github.com/zaeleus/noodles/blob/noodles-csi-0.25.1/noodles-csi/CHANGELOG.md)
  * [noodles-gff 0.22.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.22.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.18.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.45.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.45.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.31.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.31.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.26.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.26.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.42.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.42.0/noodles-vcf/CHANGELOG.md)

## 0.53.0 - 2023-10-12

  * [noodles-bam 0.47.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.47.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.38.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.38.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.25.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.25.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.44.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.44.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.25.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.25.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.30.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.30.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.9.0/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.21.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.21.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.17.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.44.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.44.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.30.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.30.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.25.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.25.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.41.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.41.0/noodles-vcf/CHANGELOG.md)

## 0.52.0 - 2023-09-21

  * [noodles-bam 0.46.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.46.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.37.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.37.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.43.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.43.0/noodles-cram/CHANGELOG.md)
  * [noodles-sam 0.43.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.43.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.24.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.24.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.40.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.40.0/noodles-vcf/CHANGELOG.md)

## 0.51.0 - 2023-09-14

  * [noodles-bam 0.45.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.45.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.36.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.36.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.42.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.42.0/noodles-cram/CHANGELOG.md)
  * [noodles-sam 0.42.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.42.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.23.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.23.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.39.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.39.0/noodles-vcf/CHANGELOG.md)

## 0.50.0 - 2023-08-31

  * [noodles-bam 0.44.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.44.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.35.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.35.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.24.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.24.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.41.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.41.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.24.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.24.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.29.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.29.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.20.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.20.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.16.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.41.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.41.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.29.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.29.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.22.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.22.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.38.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.38.0/noodles-vcf/CHANGELOG.md)

## 0.49.0 - 2023-08-24

  * [noodles-bam 0.43.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.43.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.34.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.34.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.40.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.40.0/noodles-cram/CHANGELOG.md)
  * [noodles-gff 0.19.1](https://github.com/zaeleus/noodles/blob/noodles-gff-0.19.1/noodles-gff/CHANGELOG.md)
  * [noodles-sam 0.40.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.40.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.28.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.28.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.21.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.21.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.37.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.37.0/noodles-vcf/CHANGELOG.md)

## 0.48.0 - 2023-08-17

  * [noodles-bam 0.42.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.42.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.33.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.33.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.23.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.23.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.39.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.39.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.23.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.23.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.28.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.28.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.19.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.19.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.15.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.39.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.39.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.27.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.27.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.20.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.20.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.36.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.36.0/noodles-vcf/CHANGELOG.md)

## 0.47.0 - 2023-08-03

  * [noodles-bam 0.41.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.41.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.32.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.32.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.38.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.38.0/noodles-cram/CHANGELOG.md)
  * [noodles-gff 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.18.0/noodles-gff/CHANGELOG.md)
  * [noodles-refget 0.1.0](https://github.com/zaeleus/noodles/blob/noodles-refget-0.1.0/noodles-refget/CHANGELOG.md)
  * [noodles-sam 0.38.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.38.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.26.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.26.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.19.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.19.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.35.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.35.0/noodles-vcf/CHANGELOG.md)

## 0.46.0 - 2023-07-27

  * [noodles-bam 0.40.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.40.0/noodles-bam/CHANGELOG.md)
  * [noodles-cram 0.37.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.37.0/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.27.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.27.0/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.37.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.37.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.18.0/noodles-util/CHANGELOG.md)

## 0.45.0 - 2023-07-20

  * [noodles-bam 0.39.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.39.0/noodles-bam/CHANGELOG.md)
  * [noodles-cram 0.36.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.36.0/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.26.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.26.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.17.0/noodles-gff/CHANGELOG.md)
  * [noodles-sam 0.36.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.36.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.17.0/noodles-util/CHANGELOG.md)

## 0.44.0 - 2023-07-06

  * [noodles-bam 0.38.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.38.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.31.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.31.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.35.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.35.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.22.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.22.0/noodles-csi/CHANGELOG.md)
  * [noodles-gff 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.16.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.14.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.35.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.35.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.25.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.25.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.16.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.34.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.34.0/noodles-vcf/CHANGELOG.md)

## 0.43.0 - 2023-06-29

  * [noodles-bam 0.37.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.37.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.30.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.30.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.34.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.34.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.21.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.21.0/noodles-csi/CHANGELOG.md)
  * [noodles-gff 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.15.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.13.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.34.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.34.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.24.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.24.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.15.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.33.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.33.0/noodles-vcf/CHANGELOG.md)

## 0.42.0 - 2023-06-15

  * [noodles-bam 0.36.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.36.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.29.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.29.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.10.0/noodles-bed/CHANGELOG.md)
  * [noodles-core 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.12.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.33.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.33.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.20.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.20.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.25.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.25.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.14.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.12.0/noodles-gtf/CHANGELOG.md)
  * [noodles-htsget 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-htsget-0.2.0/noodles-htsget/CHANGELOG.md)
  * [noodles-sam 0.33.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.33.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.23.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.23.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.14.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.32.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.32.0/noodles-vcf/CHANGELOG.md)

## 0.41.0 - 2023-06-08

  * [noodles-bam 0.35.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.35.0/noodles-bam/CHANGELOG.md)
  * [noodles-cram 0.32.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.32.0/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.24.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.24.0/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.32.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.32.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.13.0/noodles-util/CHANGELOG.md)

## 0.40.0 - 2023-06-01

  * [noodles-bam 0.34.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.34.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.28.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.28.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.22.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.22.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.31.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.31.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.19.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.19.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.23.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.23.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.13.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.11.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.31.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.31.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.22.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.22.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.12.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.31.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.31.0/noodles-vcf/CHANGELOG.md)

## 0.39.0 - 2023-05-18

  * [noodles-bam 0.33.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.33.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.27.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.27.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.9.0/noodles-bed/CHANGELOG.md)
  * [noodles-cram 0.30.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.30.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.18.0/noodles-csi/CHANGELOG.md)
  * [noodles-fastq 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.8.0/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.12.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.10.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.30.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.30.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.21.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.21.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.11.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.30.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.30.0/noodles-vcf/CHANGELOG.md)

## 0.38.0 - 2023-05-11

  * [noodles-bam 0.32.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.32.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.26.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.26.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.29.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.29.0/noodles-cram/CHANGELOG.md)
  * [noodles-fastq 0.7.1](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.7.1/noodles-fastq/CHANGELOG.md)
  * [noodles-gtf 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.9.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.29.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.29.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.10.0/noodles-util/CHANGELOG.md)

## 0.37.0 - 2023-05-04

  * [noodles-bam 0.31.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.31.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.25.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.25.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.28.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.28.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.17.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.22.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.22.0/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.28.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.28.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.20.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.20.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.9.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.29.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.29.0/noodles-vcf/CHANGELOG.md)

## 0.36.0 - 2023-04-27

  * [noodles-bam 0.30.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.30.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.24.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.24.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.21.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.21.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.27.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.27.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.16.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.21.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.21.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.7.0/noodles-fastq/CHANGELOG.md)
  * [noodles-sam 0.27.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.27.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.19.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.19.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.8.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.28.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.28.0/noodles-vcf/CHANGELOG.md)

## 0.35.0 - 2023-04-06

  * [noodles-bam 0.29.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.29.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.23.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.23.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.26.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.26.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.15.0/noodles-csi/CHANGELOG.md)
  * [noodles-htsget 0.1.0](https://github.com/zaeleus/noodles/blob/noodles-htsget-0.1.0/noodles-htsget/CHANGELOG.md)
  * [noodles-sam 0.26.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.26.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.18.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.7.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.27.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.27.0/noodles-vcf/CHANGELOG.md)

## 0.34.0 - 2023-03-14

  * [noodles-bam 0.28.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.28.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.22.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.22.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.25.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.25.0/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.20.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.20.0/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.25.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.25.0/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.6.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.26.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.26.0/noodles-vcf/CHANGELOG.md)

## 0.33.0 - 2023-03-03

  * [noodles-bam 0.27.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.27.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.21.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.21.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.8.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.20.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.20.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.11.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.24.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.24.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.14.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.19.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.19.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.11.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.8.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.24.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.24.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.17.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.5.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.25.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.25.0/noodles-vcf/CHANGELOG.md)

## 0.32.0 - 2023-02-03

  * [noodles-bam 0.26.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.26.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.20.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.20.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.7.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.19.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.19.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.10.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.23.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.23.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.13.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.18.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.6.0/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.10.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.7.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.23.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.23.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.16.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.4.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.24.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.24.0/noodles-vcf/CHANGELOG.md)

## 0.31.1 - 2022-12-02

  * [noodles-bcf 0.19.2](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.19.2/noodles-bcf/CHANGELOG.md)

## 0.31.0 - 2022-11-29

  * [noodles-bam 0.25.1](https://github.com/zaeleus/noodles/blob/noodles-bam-0.25.1/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.19.1](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.19.1/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.22.1](https://github.com/zaeleus/noodles/blob/noodles-cram-0.22.1/noodles-cram/CHANGELOG.md)
  * [noodles-sam 0.22.1](https://github.com/zaeleus/noodles/blob/noodles-sam-0.22.1/noodles-sam/CHANGELOG.md)
  * [noodles-util 0.3.1](https://github.com/zaeleus/noodles/blob/noodles-util-0.3.1/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.23.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.23.0/noodles-vcf/CHANGELOG.md)

## 0.30.0 - 2022-11-18

  * [noodles-bam 0.25.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.25.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.19.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.19.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.6.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.18.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.22.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.22.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.12.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.17.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gtf 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.6.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.22.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.22.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.15.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.3.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.22.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.22.0/noodles-vcf/CHANGELOG.md)

## 0.29.0 - 2022-10-28

  * [noodles-bam 0.24.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.24.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.18.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.17.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.21.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.21.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.11.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.16.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.5.1](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.5.1/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.9.0/noodles-gff/CHANGELOG.md)
  * [noodles-sam 0.21.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.21.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.14.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.2.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.21.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.21.0/noodles-vcf/CHANGELOG.md)

## 0.28.0 - 2022-10-20

  * [noodles-bam 0.23.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.23.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.17.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.5.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.16.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.9.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.20.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.20.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.10.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.15.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.8.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.5.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.20.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.20.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.13.0/noodles-tabix/CHANGELOG.md)
  * [noodles-util 0.1.0](https://github.com/zaeleus/noodles/blob/noodles-util-0.1.0/noodles-util/CHANGELOG.md)
  * [noodles-vcf 0.20.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.20.0/noodles-vcf/CHANGELOG.md)

## 0.27.0 - 2022-09-29

  * [noodles-bam 0.22.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.22.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.16.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.15.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.19.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.19.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.9.1](https://github.com/zaeleus/noodles/blob/noodles-csi-0.9.1/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.14.0/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.19.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.19.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.12.1](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.12.1/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.19.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.19.0/noodles-vcf/CHANGELOG.md)

## 0.26.0 - 2022-08-16

  * [noodles-bam 0.21.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.21.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.15.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.4.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.14.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.8.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.18.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.9.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.13.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.7.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.4.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.18.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.12.0/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.18.0/noodles-vcf/CHANGELOG.md)

## 0.25.0 - 2022-07-05

  * [noodles-bam 0.20.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.20.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.14.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.13.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.17.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.8.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.12.0/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.17.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.11.0/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.17.0/noodles-vcf/CHANGELOG.md)

## 0.24.0 - 2022-06-09

  * [noodles-tabix 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.10.0/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.16.1](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.16.1/noodles-vcf/CHANGELOG.md)

## 0.23.0 - 2022-06-08

  * [noodles-bam 0.19.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.19.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.13.3](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.13.3/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.3.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.12.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.7.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.16.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.7.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.11.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.6.1](https://github.com/zaeleus/noodles/blob/noodles-gff-0.6.1/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.3.1](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.3.1/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.16.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.9.1](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.9.1/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.16.0/noodles-vcf/CHANGELOG.md)

## 0.22.0 - 2022-04-14

  * [noodles-bam 0.18.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.18.0/noodles-bam/CHANGELOG.md)
  * [noodles-cram 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.15.0/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.10.0/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.15.0/noodles-sam/CHANGELOG.md)

## 0.21.0 - 2022-03-29

  * [noodles-bam 0.17.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.17.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.13.2](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.13.2/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.2.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.11.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.6.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.14.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.6.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.9.0/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.6.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.3.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.14.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.9.0/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.15.0/noodles-vcf/CHANGELOG.md)

## 0.20.0 - 2022-03-02

  * [noodles-bam 0.16.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.16.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.13.1](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.13.1/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.10.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.5.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.13.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.5.1](https://github.com/zaeleus/noodles/blob/noodles-csi-0.5.1/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.8.0/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.13.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.8.1](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.8.1/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.14.1](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.14.1/noodles-vcf/CHANGELOG.md)

## 0.19.0 - 2022-02-17

  * [noodles-bam 0.15.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.15.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.13.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bed 0.1.0](https://github.com/zaeleus/noodles/blob/noodles-bed-0.1.0/noodles-bed/CHANGELOG.md)
  * [noodles-bgzf 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.9.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.4.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.12.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.5.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.7.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.5.0/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.5.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.2.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.12.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.8.0/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.14.0/noodles-vcf/CHANGELOG.md)

## 0.18.0 - 2022-01-27

  * [noodles-bam 0.14.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.14.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.12.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.8.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.3.4](https://github.com/zaeleus/noodles/blob/noodles-core-0.3.4/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.11.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.4.3](https://github.com/zaeleus/noodles/blob/noodles-csi-0.4.3/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.6.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.4.0/noodles-fastq/CHANGELOG.md)
  * [noodles-sam 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.11.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.7.3](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.7.3/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.13.0/noodles-vcf/CHANGELOG.md)

## 0.17.0 - 2022-01-13

  * [noodles-bam 0.13.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.13.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.11.0/noodles-bcf/CHANGELOG.md)
  * [noodles-core 0.3.3](https://github.com/zaeleus/noodles/blob/noodles-core-0.3.3/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.10.0/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.5.2](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.5.2/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.10.0/noodles-sam/CHANGELOG.md)
  * [noodles-vcf 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.12.0/noodles-vcf/CHANGELOG.md)

## 0.16.0 - 2021-12-16

  * [noodles-bam 0.12.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.12.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.10.0/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.9.0/noodles-cram/CHANGELOG.md)

## 0.15.0 - 2021-12-09

  * [noodles-bam 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.11.0/noodles-bam/CHANGELOG.md)
  * [noodles-core 0.3.2](https://github.com/zaeleus/noodles/blob/noodles-core-0.3.2/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.8.3](https://github.com/zaeleus/noodles/blob/noodles-cram-0.8.3/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.5.1](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.5.1/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.9.0/noodles-sam/CHANGELOG.md)
  * [noodles-vcf 0.11.1](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.11.1/noodles-vcf/CHANGELOG.md)

## 0.14.0 - 2021-12-02

  * [noodles-bam 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.10.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.9.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.7.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.8.2](https://github.com/zaeleus/noodles/blob/noodles-cram-0.8.2/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.4.2](https://github.com/zaeleus/noodles/blob/noodles-csi-0.4.2/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.5.0/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.8.1](https://github.com/zaeleus/noodles/blob/noodles-sam-0.8.1/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.7.2](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.7.2/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.11.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.11.0/noodles-vcf/CHANGELOG.md)

## 0.13.0 - 2021-11-18

  * [noodles-bam 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.9.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.8.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.6.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.3.1](https://github.com/zaeleus/noodles/blob/noodles-core-0.3.1/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.8.1](https://github.com/zaeleus/noodles/blob/noodles-cram-0.8.1/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.4.1](https://github.com/zaeleus/noodles/blob/noodles-csi-0.4.1/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.4.1](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.4.1/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.8.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.7.1](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.7.1/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.10.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.10.0/noodles-vcf/CHANGELOG.md)

## 0.12.0 - 2021-11-11

  * [noodles-bam 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.8.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.7.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.5.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.3.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.8.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.4.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.4.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.3.0/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.4.0/noodles-gff/CHANGELOG.md)
  * [noodles-gtf 0.1.0](https://github.com/zaeleus/noodles/blob/noodles-gtf-0.1.0/noodles-gtf/CHANGELOG.md)
  * [noodles-sam 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.7.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.7.0/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.9.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.9.0/noodles-vcf/CHANGELOG.md)

## 0.11.0 - 2021-10-16

  * [noodles-bam 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.7.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.6.1](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.6.1/noodles-bcf/CHANGELOG.md)
  * [noodles-core 0.2.3](https://github.com/zaeleus/noodles/blob/noodles-core-0.2.3/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.7.0/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.3.1](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.3.1/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.3.0/noodles-gff/CHANGELOG.md)
  * [noodles-sam 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.6.0/noodles-sam/CHANGELOG.md)
  * [noodles-vcf 0.8.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.8.0/noodles-vcf/CHANGELOG.md)

## 0.10.1 - 2021-10-02

  * [noodles-bam 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.6.0/noodles-bam/CHANGELOG.md)
  * [noodles-cram 0.6.1](https://github.com/zaeleus/noodles/blob/noodles-cram-0.6.1/noodles-cram/CHANGELOG.md)

## 0.10.0 - 2021-10-01

  * [noodles-bam 0.5.2](https://github.com/zaeleus/noodles/blob/noodles-bam-0.5.2/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.6.0/noodles-bcf/CHANGELOG.md)
  * [noodles-core 0.2.2](https://github.com/zaeleus/noodles/blob/noodles-core-0.2.2/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.6.0/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.3.0/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.2.0/noodles-fastq/CHANGELOG.md)
  * [noodles-sam 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.5.0/noodles-sam/CHANGELOG.md)
  * [noodles-vcf 0.7.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.7.0/noodles-vcf/CHANGELOG.md)

## 0.9.0 - 2021-09-23

  * [noodles-bam 0.5.1](https://github.com/zaeleus/noodles/blob/noodles-bam-0.5.1/noodles-bam/CHANGELOG.md)
  * [noodles-core 0.2.1](https://github.com/zaeleus/noodles/blob/noodles-core-0.2.1/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.5.1](https://github.com/zaeleus/noodles/blob/noodles-cram-0.5.1/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.2.4](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.2.4/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.4.0/noodles-sam/CHANGELOG.md)
  * [noodles-vcf 0.6.2](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.6.2/noodles-vcf/CHANGELOG.md)

## 0.8.0 - 2021-09-19

  * [noodles-bam 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.5.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.5.2](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.5.2/noodles-bcf/CHANGELOG.md)
  * [noodles-core 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-core-0.2.0/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.5.0/noodles-cram/CHANGELOG.md)
  * [noodles-fasta 0.2.3](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.2.3/noodles-fasta/CHANGELOG.md)
  * [noodles-gff 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-gff-0.2.0/noodles-gff/CHANGELOG.md)
  * [noodles-sam 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.3.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.6.1](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.6.1/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.6.1](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.6.1/noodles-vcf/CHANGELOG.md)

## 0.7.0 - 2021-09-01

  * [noodles-bcf 0.5.1](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.5.1/noodles-bcf/CHANGELOG.md)
  * [noodles-cram 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.4.0/noodles-cram/CHANGELOG.md)
  * [noodles-vcf 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.6.0/noodles-vcf/CHANGELOG.md)

## 0.6.0 - 2021-08-19

  * [noodles-bam 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.4.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.5.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.4.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.3.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.3.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.2.2](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.2.2/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.2.2](https://github.com/zaeleus/noodles/blob/noodles-sam-0.2.2/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.6.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.6.0/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.5.0/noodles-vcf/CHANGELOG.md)

## 0.5.0 - 2021-08-11

  * [noodles-bam 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.3.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.4.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.3.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-cram 0.2.2](https://github.com/zaeleus/noodles/blob/noodles-cram-0.2.2/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.2.2](https://github.com/zaeleus/noodles/blob/noodles-csi-0.2.2/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.2.1](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.2.1/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.2.1](https://github.com/zaeleus/noodles/blob/noodles-sam-0.2.1/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.5.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.5.0/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.4.0/noodles-vcf/CHANGELOG.md)

## 0.4.0 - 2021-08-04

  * [noodles-bcf 0.3.1](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.3.1/noodles-bcf/CHANGELOG.md)
  * [noodles-tabix 0.4.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.4.0/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.3.0/noodles-vcf/CHANGELOG.md)

## 0.3.0 - 2021-07-30

  * [noodles-bam 0.2.1](https://github.com/zaeleus/noodles/blob/noodles-bam-0.2.1/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.3.0/noodles-bcf/CHANGELOG.md)
  * [noodles-core 0.1.2](https://github.com/zaeleus/noodles/blob/noodles-core-0.1.2/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.2.1](https://github.com/zaeleus/noodles/blob/noodles-cram-0.2.1/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.2.1](https://github.com/zaeleus/noodles/blob/noodles-csi-0.2.1/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.2.0/noodles-fasta/CHANGELOG.md)
  * [noodles-sam 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-sam-0.2.0/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.3.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.3.0/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.2.0/noodles-vcf/CHANGELOG.md)

## 0.2.0 - 2021-07-21

  * [noodles-bam 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-bam-0.2.0/noodles-bam/CHANGELOG.md)
  * [noodles-bcf 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-bcf-0.2.0/noodles-bcf/CHANGELOG.md)
  * [noodles-bgzf 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-bgzf-0.2.0/noodles-bgzf/CHANGELOG.md)
  * [noodles-core 0.1.1](https://github.com/zaeleus/noodles/blob/noodles-core-0.1.1/noodles-core/CHANGELOG.md)
  * [noodles-cram 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-cram-0.2.0/noodles-cram/CHANGELOG.md)
  * [noodles-csi 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-csi-0.2.0/noodles-csi/CHANGELOG.md)
  * [noodles-fasta 0.1.1](https://github.com/zaeleus/noodles/blob/noodles-fasta-0.1.1/noodles-fasta/CHANGELOG.md)
  * [noodles-fastq 0.1.1](https://github.com/zaeleus/noodles/blob/noodles-fastq-0.1.1/noodles-fastq/CHANGELOG.md)
  * [noodles-gff 0.1.1](https://github.com/zaeleus/noodles/blob/noodles-gff-0.1.1/noodles-gff/CHANGELOG.md)
  * [noodles-sam 0.1.1](https://github.com/zaeleus/noodles/blob/noodles-sam-0.1.1/noodles-sam/CHANGELOG.md)
  * [noodles-tabix 0.2.0](https://github.com/zaeleus/noodles/blob/noodles-tabix-0.2.0/noodles-tabix/CHANGELOG.md)
  * [noodles-vcf 0.1.1](https://github.com/zaeleus/noodles/blob/noodles-vcf-0.1.1/noodles-vcf/CHANGELOG.md)

## 0.1.0 - 2021-07-14

  * Initial release.
