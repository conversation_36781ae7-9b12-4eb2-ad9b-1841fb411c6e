[package]
name = "noodles-bed"
version = "0.27.0"
authors = ["<PERSON> <<PERSON><PERSON><PERSON>@gmail.com>"]
license.workspace = true
edition.workspace = true
rust-version.workspace = true
description = "BED (Browser Extensible Data) reader and writer"
homepage = "https://github.com/zaeleus/noodles"
repository = "https://github.com/zaeleus/noodles"
documentation = "https://docs.rs/noodles-bed"
categories = ["parser-implementations", "science::bioinformatics"]

[dependencies]
bstr.workspace = true
lexical-core.workspace = true
memchr.workspace = true
noodles-bgzf = { path = "../noodles-bgzf", version = "0.42.0" }
noodles-core = { path = "../noodles-core", version = "0.18.0" }
noodles-csi = { path = "../noodles-csi", version = "0.50.0" }
noodles-tabix = { path = "../noodles-tabix", version = "0.56.0" }

[lints]
workspace = true
