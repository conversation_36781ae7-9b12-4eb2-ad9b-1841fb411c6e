name: CI

on: [push, pull_request]

jobs:
  format:
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - name: Update Rust
        run: rustup update stable && rustup default stable
      - name: Install rustfmt
        run: rustup component add rustfmt
      - run: cargo fmt -- --check

  lint:
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - name: Update Rust
        run: rustup update stable && rustup default stable
      - name: Install clippy
        run: rustup component add clippy
      - run: cargo clippy --all-features -- --deny warnings

  test:
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - name: Update Rust
        run: rustup update stable && rustup default stable
      - run: cargo test --all-features
