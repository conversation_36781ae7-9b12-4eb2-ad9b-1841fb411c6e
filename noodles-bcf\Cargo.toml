[package]
name = "noodles-bcf"
version = "0.77.0"
authors = ["<PERSON> <z<PERSON><PERSON>@gmail.com>"]
license.workspace = true
edition.workspace = true
rust-version.workspace = true
description = "Binary Call Format (BCF) reader and writer"
homepage = "https://github.com/zaeleus/noodles"
repository = "https://github.com/zaeleus/noodles"
documentation = "https://docs.rs/noodles-bcf"
categories = ["parser-implementations", "science::bioinformatics"]

[features]
async = [
  "dep:futures",
  "dep:pin-project-lite",
  "dep:tokio",
  "noodles-bgzf/async",
  "noodles-csi/async",
  "noodles-vcf/async",
]

[dependencies]
byteorder.workspace = true
indexmap.workspace = true
memchr.workspace = true
noodles-bgzf = { path = "../noodles-bgzf", version = "0.42.0" }
noodles-core = { path = "../noodles-core", version = "0.18.0" }
noodles-csi = { path = "../noodles-csi", version = "0.50.0" }
noodles-vcf = { path = "../noodles-vcf", version = "0.80.0" }

futures = { workspace = true, optional = true, features = ["std"] }
pin-project-lite = { workspace = true, optional = true }
tokio = { workspace = true, optional = true, features = ["io-util"] }

[dev-dependencies]
tokio = { workspace = true, features = ["fs", "io-std", "macros", "rt-multi-thread"] }

[lints]
workspace = true

[package.metadata.docs.rs]
features = ["async"]

[[example]]
name = "bcf_count_async"
required-features = ["async"]

[[example]]
name = "bcf_query_async"
required-features = ["async"]

[[example]]
name = "bcf_read_header_async"
required-features = ["async"]

[[example]]
name = "bcf_view_async"
required-features = ["async"]
